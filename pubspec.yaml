name: signal_lab
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.9.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_inappwebview: ^6.1.5
  get: ^4.6.6
  google_fonts: ^6.2.0
  dotted_border: ^3.1.0
  flutter_svg: ^2.0.10+1
  flutter_svg_provider: ^1.0.7
  http: ^1.0.0
  lottie: ^3.1.2
  carousel_slider: ^5.1.1
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3
  intl: ^0.20.2
  pin_code_fields: ^8.0.1
  webview_flutter: ^4.7.0
  url_launcher: ^6.2.6
  flutter_widget_from_html: ^0.17.0
  font_awesome_flutter: ^10.7.0
  animated_bottom_navigation_bar: ^1.3.3
  auto_size_text: ^3.0.0
  simple_animations: ^5.0.2
  firebase_messaging: ^16.0.0
  flutter_local_notifications: ^19.4.0
  firebase_analytics: ^12.0.0
  firebase_core: ^4.0.0
  awesome_dialog: ^3.2.1
  #  firebase_core_platform_interface: 5.0.0
  permission_handler: ^12.0.1
  google_sign_in: ^7.1.1
  equatable: ^2.0.5
  json_annotation: ^4.9.0
  flutter_spinkit: ^5.2.1
  cached_network_image: ^3.3.1
  file_picker: ^10.3.2
  shimmer: ^3.0.0
  open_file: ^3.3.2
  rename: ^3.1.0
  cupertino_icons: ^1.0.8

  ## -- OnBoarding PACKAGES
  liquid_swipe: ^3.1.0
  smooth_page_indicator: ^1.2.1
  get_storage: ^2.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/on_boarding_images/
    - assets/images/social/
    - assets/icons/
