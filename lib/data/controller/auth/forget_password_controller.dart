import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/helper/shared_pref_helper.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/data/model/auth/verification/email_verification_model.dart';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/auth/login/login_repo.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';
import 'package:signal_lab/views/screens/auth/registration/model/error_model.dart';


class ForgetPasswordController extends GetxController {
  LoginRepo loginRepo;

TextEditingController emailOrUsernameController = TextEditingController();
  TextEditingController passController        = TextEditingController();
  List<String> errors = [];
  String email='';
  String password='';
  
  String confirmPassword='';
  bool isLoading = false;
  bool remember = false;
  bool hasError = false;
  String currentText = "";

  ForgetPasswordController({required this.loginRepo}){
    checkPasswordStrength = loginRepo.apiClient.needSecurePassword();
  }

  addError({required String error}) {
    if (!errors.contains(error)) {
      errors.add(error);
      update();
    }
  }

  removeError({required String error}) {
    if (errors.contains(error)) {
      errors.remove(error);
      update();
    }
  }

   void submitForgetPassCode() async {

    String input = emailOrUsernameController.text;

    if(input.isEmpty){
      MySnackbar.error(errorList: [MyStrings.enterYourEmail]);
      return;
    }

    isLoading = true;
    update();

    String type = input.contains('@') ? 'email' : 'username';
    String responseEmail = await loginRepo.forgetPassword(type, input);

    if(responseEmail.isNotEmpty){
      emailOrUsernameController.text = '';
      Get.toNamed(RouteHelper.verifyPassCodeScreen, arguments: responseEmail);
    }

    isLoading = false;
    update();
  }

  bool isResendLoading=false;
  void resendForgetPassCode() async {
    isResendLoading = true;
    update();
    String value = email;
    String type = 'email';
    await loginRepo.forgetPassword(type, value);
    isResendLoading = false;
    update();
  }

  bool verifyLoading=false;

  void verifyForgetPasswordCode(String value) async{
    if(value.isNotEmpty){
      verifyLoading = true;
      update();
      EmailVerificationModel model = await loginRepo.verifyForgetPassCode(value);

      if(model.status == 'success'){
        verifyLoading = false;
        Get.offAndToNamed(RouteHelper.resetPasswordScreen,arguments: email);
        clearAllData();
      }else{
        verifyLoading = false;
        update();
        List<String>errorList = [MyStrings.verificationFailed];
        MySnackbar.error(errorList: model.message?.error??errorList);
      }
    }
  }


  bool checkPasswordStrength = false;

  final FocusNode emailFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();
  final FocusNode confirmPasswordFocusNode = FocusNode();

  void resetPassword() async {

    String password = passController.text;
    isLoading = true;
    update();

    ResponseModel responseModel = await loginRepo.resetPassword(email,password,currentText);
    if(responseModel.statusCode == 200){
      EmailVerificationModel model = EmailVerificationModel.fromJson(jsonDecode(responseModel.responseJson));
      if(model.status == 'success'){
        MySnackbar.success(msg: model.message?.success??[MyStrings.requestSuccess]);
        loginRepo.apiClient.sharedPreferences.remove(SharedPreferenceHelper.resetPassTokenKey);
        Get.offAndToNamed(RouteHelper.signInScreen);
      } else{
        MySnackbar.success(msg: model.message?.error??[MyStrings.requestFail]);
      }
    } else{
      MySnackbar.success(msg: [responseModel.message]);
    }

    isLoading = false;
    update();
  }

  clearAllData(){
    isLoading = false;
    currentText = '';
  }

  List<ErrorModel> passwordValidationRulse = [
    ErrorModel(text: MyStrings.hasUpperLetter, hasError: true),
    ErrorModel(text: MyStrings.hasLowerLetter, hasError: true),
    ErrorModel(text: MyStrings.hasDigit, hasError: true),
    ErrorModel(text: MyStrings.hasSpecialChar, hasError: true),
    ErrorModel(text: MyStrings.minSixChar, hasError: true),
  ];


  bool hasPasswordFocus = false;
  void changePasswordFocus(bool hasFocus) {
    hasPasswordFocus = hasFocus;
    update();
  }

  void updateValidationList(String value){
    passwordValidationRulse[0].hasError = value.contains(RegExp(r'[A-Z]'))?false:true;
    passwordValidationRulse[1].hasError = value.contains(RegExp(r'[a-z]'))?false:true;
    passwordValidationRulse[2].hasError = value.contains(RegExp(r'[0-9]'))?false:true;
    passwordValidationRulse[3].hasError = value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))?false:true;
    passwordValidationRulse[4].hasError = value.length>=6?false:true;

    update();
  }

  RegExp regex = RegExp(r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#$%^&*(),.?":{}|<>]).{6,}$');
  String? validatePassword(String value) {
    if (value.isEmpty) {
      return null;
    } else {
      if(checkPasswordStrength){
        if (!regex.hasMatch(value)) {
          String message = MyStrings.strongPass;
          return message;
        } else {
          return null;
        }
      }else{
        return null;
      }
    }
  }


}
