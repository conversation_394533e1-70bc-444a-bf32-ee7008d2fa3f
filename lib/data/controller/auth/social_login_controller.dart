import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/model/general_setting/general_setting_response_model.dart';
import 'package:signal_lab/data/repo/auth/social_login_repo.dart';
import 'package:signal_lab/views/components/signin_with_linkdin/signin_with_linkedin.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';

import '../../model/auth/login/login_response_model.dart';
import '../../model/global/response_model/response_model.dart';
import '../../model/user/user.dart';

class SocialLoginController extends GetxController {

  SocialLoginRepo repo;
  SocialLoginController({required this.repo});
 

  // final GoogleSignIn googleSignIn = GoogleSignIn(); //old
  // final GoogleSignIn googleSignIn = GoogleSignIn.instance;
  final _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;
  bool isGoogleSignInLoading = false;

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
    } catch (e) {
      print('.....Failed to initialize Google Sign-In: $e');
    }
  }

  /// Always check Google sign in initialization before use
  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      isGoogleSignInLoading = true;
      update();
      await _ensureGoogleSignInInitialized();

      try {
        // authenticate() throws exceptions instead of returning null
        final GoogleSignInAccount account = await _googleSignIn.authenticate(
          scopeHint: ['email'],  // Specify required scopes
        );
        final authClient = account.authorizationClient;
        final authorization = await authClient.authorizeScopes(['email']);
        print('.....${authorization.accessToken}');
        await socialLoginUser(provider: 'google', accessToken: authorization.accessToken);
        // return account;
      } on GoogleSignInException catch (e) {
        print('Google Sign In error: code: ${e.code.name} description:${e.description} details:${e.details}, error: $e');
            rethrow;
        } catch (error) {
          print('Unexpected Google Sign-In error: $error');
          rethrow;
        }
      // if (googleUser == null) {
      //   isGoogleSignInLoading = false;
      //   update();
      //   return;
      // }

      // final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      // // Get authorization for Firebase scopes if needed
      // final authClient = googleSignIn.authorizationClient;
      // final authorization = await authClient.authorizationForScopes(['email']);
      // await socialLoginUser(provider: 'google', accessToken: authorization == null ? '' : authorization.accessToken);
    } catch (e) {
      debugPrint(e.toString());
      MySnackbar.error(errorList: [e.toString()]);
    }

    isGoogleSignInLoading = false;
    update();
  }

  //SIGN IN With LinkeDin
  bool isLinkedinLoading = false;
  Future<void> signInWithLinkedin(BuildContext context) async {
    try {
      isLinkedinLoading = false;
      update();

      SocialiteCredentials linkedinCredential = repo.apiClient.getSocialCredentialsConfigData();
      String linkedinCredentialRedirectUrl = "${repo.apiClient.getSocialCredentialsRedirectUrl()}/linkedin";
      print(linkedinCredentialRedirectUrl);
      print(linkedinCredential.linkedin?.toJson());
      SignInWithLinkedIn.signIn(
        context,
        config: LinkedInConfig(clientId: linkedinCredential.linkedin?.clientId ?? '', clientSecret: linkedinCredential.linkedin?.clientSecret ?? '', scope: ['openid', 'profile', 'email'], redirectUrl: "$linkedinCredentialRedirectUrl"),
        onGetAuthToken: (data) {
          print('Auth token data: ${data.toJson()}');
        },
        onGetUserProfile: (token, user) async {
          print('${token.idToken}-');
          print('LinkedIn User: ${user.toJson()}');
          await socialLoginUser(provider: 'linkedin', accessToken: token.accessToken ?? '');
        },
        onSignInError: (error) {
          print('Error on sign in: $error');
          MySnackbar.error(errorList: [error.description!] ?? [MyStrings.loginFailedTryAgain.tr]);
          isLinkedinLoading = false;
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());

      MySnackbar.error(errorList: [e.toString()]);
    }
  }

  Future socialLoginUser({
    String accessToken = '',
    String? provider,
  }) async {
    try {
      ResponseModel responseModel = await repo.socialLoginUser(
        accessToken: accessToken,
        provider: provider,
      );
      if (responseModel.statusCode == 200) {
        LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(responseModel.responseJson));
        if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
          String accessToken = loginModel.data?.accessToken ?? "";
          String tokenType = loginModel.data?.tokenType ?? "";
          User? user = loginModel.data?.user;
          await RouteHelper.checkUserStatusAndGoToNextStep(user,accessToken: accessToken,tokenType: tokenType,isRemember: true);
        } else {
          MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain.tr]);
        }
      } else {
        MySnackbar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      //printx(e.toString());
    }
  }

  bool checkSocialAuthActiveOrNot({String provider = 'all'}) {
    if (provider == 'google') {
      return repo.apiClient.getSocialCredentialsConfigData().google?.status == '1';
    } else if (provider == 'facebook') {
      return repo.apiClient.getSocialCredentialsConfigData().facebook?.status == '1';
    } else if (provider == 'linkedin') {
      return repo.apiClient.getSocialCredentialsConfigData().linkedin?.status == '1';
    } else {
      return repo.apiClient.isSocialAnyOfSocialLoginOptionEnable();
    }
  }
}