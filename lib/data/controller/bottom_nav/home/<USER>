import 'dart:convert';

import 'package:get/get.dart';
import 'package:signal_lab/core/helper/date_converter.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/model/bottom_nav/home/<USER>';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/bottom_nav/home/<USER>';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';

class HomeController extends GetxController {
  HomeRepo dashboardRepo;
  HomeController({required this.dashboardRepo});

  bool isLoading = true;
  List<LatestSignal> signalsList = [];

  String totalTrx = '';
  String totalSignal = "";
  String totalDeposit = "";
  String totalReferral = "";
  String referralLink = "";
  String balance = "";

  String packageId = "";
  String packageName = "";
  String packageTime = "";
  String packagePrice = "";
  String packageValidity = "";

  String currencySymbol = '';
  String currency = '';

Future<void> getDashboardData() async {
  currencySymbol = dashboardRepo.apiClient.getCurrencyOrUsername(isCurrency: true, isSymbol: true);
  currency = dashboardRepo.apiClient.getCurrencyOrUsername(isCurrency: true, isSymbol: false);
  signalsList.clear();

  ResponseModel responseModel = await dashboardRepo.getDashboardData();

  if (responseModel.statusCode == 200) {
    DashboardModel dashboardModel = DashboardModel.fromJson(jsonDecode(responseModel.responseJson));
    if (dashboardModel.status.toString().toLowerCase() == "success") {
      List<LatestSignal>? tempSignalList = dashboardModel.data?.latestSignals;
      totalTrx = dashboardModel.data?.totalTrx ?? '00';
      totalSignal = dashboardModel.data?.totalSignal ?? "00";
      totalDeposit = dashboardModel.data?.totalDeposit ?? "00";
      totalReferral = dashboardModel.data?.totalReferral ?? "00";
      referralLink = dashboardModel.data?.referralLink ?? "00";
      balance = dashboardModel.data?.user?.balance ?? "00";

      packageName = dashboardModel.data?.user?.package?.name ?? "Package: N/A";
      String? planTime = dashboardModel.data?.user?.validity;
      print("this is date--------------${dashboardModel.data?.user?.validity}");
      print("this is date${planTime}");
      print("this is balance${dashboardModel.data?.user?.balance}");

      if (planTime == null || planTime.isEmpty) {
        packageTime = '---';
      } else {
        try {
          packageTime = DateConverter.formatValidityDate(planTime);
        } catch (e) {
          print("Error parsing date: $e");
          packageTime = '---';
        }
      }
      
      packagePrice = dashboardModel.data?.user?.package?.price ?? "";
      packageValidity = dashboardModel.data?.user?.package?.validity ?? "";
      packageId = dashboardModel.data?.user?.package?.id.toString() ?? "";

      if (tempSignalList != null && tempSignalList.isNotEmpty) {
        signalsList.addAll(tempSignalList);
      }
    } else {
      MySnackbar.error(errorList: dashboardModel.message?.error ?? [MyStrings.somethingWentWrong]);
    }
  } else {
    MySnackbar.error(errorList: [responseModel.message]);
  }

  isLoading = false;
  update();
}
}
