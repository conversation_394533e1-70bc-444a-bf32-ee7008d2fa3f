import 'dart:convert';

import 'package:get/get.dart';
import 'package:signal_lab/core/helper/shared_pref_helper.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/messages.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/controller/localization/localization_controller.dart';
import 'package:signal_lab/data/model/general_setting/general_setting_response_model.dart';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/auth/general_setting_repo.dart';
import 'package:signal_lab/push_notification_service.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';
import 'package:get_storage/get_storage.dart';

import '../../views/screens/on_boarding/on_boarding_screen.dart';

class SplashController extends GetxController {
  GeneralSettingRepo repo;
  LocalizationController localizationController;
  SplashController({required this.repo, required this.localizationController});
  final deviceStorage = GetStorage();
  bool isLoading = true;
  gotoNextPage() async {
    await loadLanguage();
    bool isRemember = repo.apiClient.sharedPreferences.getBool(SharedPreferenceHelper.rememberMeKey) ?? false;

    noInternet = false;

    update();

    deviceStorage.writeIfNull('isFirstTime', true);
    final bool isFirstTime = deviceStorage.read('isFirstTime') as bool;
    if(!isFirstTime){
      PushNotificationService(apiClient: Get.find()).sendUserToken();
    }

    storeLangDataInLocalStorage();

    loadAndSaveGeneralSettingsData(isRemember, isFirstTime);
  }

  bool noInternet = false;
  void loadAndSaveGeneralSettingsData(bool isRemember, bool isFirstTime) async {
    ResponseModel response = await repo.getGeneralSetting();

    if (response.statusCode == 200) {
      GeneralSettingResponseModel model = GeneralSettingResponseModel.fromJson(jsonDecode(response.responseJson));
      if (model.status?.toLowerCase() == MyStrings.success) {
        repo.apiClient.storeGeneralSetting(model);
      } else {
        List<String> message = [MyStrings.somethingWentWrong];
        MySnackbar.error(errorList: model.message?.error ?? message);
      }
    } else {
      if (response.statusCode == 503) {
        noInternet = true;
        update();
      }
      MySnackbar.error(errorList: [response.message]);
    }

    isLoading = false;
    update();



    if(isFirstTime){
      // First launch: go to OnBoarding
      deviceStorage.write('isFirstTime', false);

      await Get.offAll(() => const OnBoardingScreen());
      // Get.offAndToNamed(RouteHelper.onBoardScreen);
      // After showing onboarding, mark it done:
    }
    else if (isRemember) {

      Future.delayed(const Duration(seconds: 1), () {
        Get.offAndToNamed(RouteHelper.homeScreen);
      });
    } else {
      // Not first launch: go to Login
      Future.delayed(const Duration(seconds: 1), () {
        Get.offAndToNamed(RouteHelper.signInScreen);
      });
    }
  }

  Future<bool> storeLangDataInLocalStorage() {
    if (!repo.apiClient.sharedPreferences.containsKey(SharedPreferenceHelper.countryCode)) {
      return repo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.countryCode, MyStrings.myLanguages[0].countryCode);
    }
    if (!repo.apiClient.sharedPreferences.containsKey(SharedPreferenceHelper.languageCode)) {
      return repo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.languageCode, MyStrings.myLanguages[0].languageCode);
    }
    return Future.value(true);
  }

  Future<void> loadLanguage() async {
    localizationController.loadCurrentLanguage();
    String languageCode = localizationController.locale.languageCode;

    ResponseModel response = await repo.getLanguage(languageCode);
    if (response.statusCode == 200) {
      try {
        Map<String, Map<String, String>> language = {};
        var resJson = jsonDecode(response.responseJson);
        saveLanguageList(response.responseJson);
        var value = resJson['data']['file'].toString() == '[]' ? {} : resJson['data']['file'];
        Map<String, String> json = {};
        value.forEach((key, value) {
          json[key] = value.toString();
        });
        language['${localizationController.locale.languageCode}_${localizationController.locale.countryCode}'] = json;
        Get.addTranslations(Messages(languages: language).keys);
      } catch (e) {
        MySnackbar.error(errorList: [e.toString()]);
      }
    } else {
      MySnackbar.error(errorList: [response.message]);
    }
  }

  void saveLanguageList(String languageJson) async {
    await repo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.languageListKey, languageJson);
    return;
  }
}
