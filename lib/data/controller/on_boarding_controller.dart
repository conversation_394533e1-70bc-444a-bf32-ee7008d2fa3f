import 'package:get/get.dart';
import 'package:liquid_swipe/PageHelpers/LiquidController.dart';
import 'package:get_storage/get_storage.dart';
import 'package:signal_lab/core/utils/my_color.dart';

import '../../core/route/route.dart';
import '../../core/utils/my_images.dart';
import '../../core/utils/my_strings.dart';
import '../../views/screens/auth/login/sign_in_screen.dart';
import '../../views/screens/on_boarding/components/on_boarding_page_widget.dart';
import 'on_boarding/mode_on_boarding.dart';

class OnBoardingController extends GetxController {
  //Variables
  final userStorage = GetStorage(); // Use for local Storage
  final controller = LiquidController();
  RxInt currentPage = 0.obs;

  //Functions to trigger Skip, Next and onPageChange Events
  skip() => controller.jumpToPage(page: 2);

  animateToNextSlide() => controller.animateToPage(page: controller.currentPage + 1);

  animateToNextSlideWithLocalStorage() {
    if (controller.currentPage == 2) {
      userStorage.write('isFirstTime', false);
      // Get.offAll(() => const SignInScreen());
      Get.offAndToNamed(RouteHelper.signInScreen);
    } else {
      controller.animateToPage(page: controller.currentPage + 1);
    }
  }

  onPageChangedCallback(int activePageIndex){
    print('....${activePageIndex} ....');

    currentPage.value = activePageIndex;
  }

  //Three Onboarding Pages
  final pages = [
    OnBoardingPageWidget(
      model: OnBoardingModel(
        image: MyImages.tOnBoardingImage1,
        title: MyStrings.tOnBoardingTitle1,
        subTitle: MyStrings.tOnBoardingSubTitle1,
        counterText: MyStrings.tOnBoardingCounter1,
        bgColor: MyColor.onBoardingPage1Color,
      ),
    ),
    OnBoardingPageWidget(
      model: OnBoardingModel(
        image: MyImages.tOnBoardingImage2,
        title: MyStrings.tOnBoardingTitle2,
        subTitle: MyStrings.tOnBoardingSubTitle2,
        counterText: MyStrings.tOnBoardingCounter2,
        bgColor: MyColor.onBoardingPage2Color,
      ),
    ),
    OnBoardingPageWidget(
      model: OnBoardingModel(
        image: MyImages.tOnBoardingImage3,
        title: MyStrings.tOnBoardingTitle3,
        subTitle: MyStrings.tOnBoardingSubTitle3,
        counterText: MyStrings.tOnBoardingCounter3,
        bgColor: MyColor.onBoardingPage3Color,
      ),
    ),
  ];
}