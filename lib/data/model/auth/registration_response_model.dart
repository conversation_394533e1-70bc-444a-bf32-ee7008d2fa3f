// To parse this JSON data, do
//
//     final registrationResponseModel = registrationResponseModelFromJson(jsonString);

import 'dart:convert';

import '../global/meassage_model.dart';

RegistrationResponseModel registrationResponseModelFromJson(String str) => RegistrationResponseModel.fromJson(json.decode(str));

String registrationResponseModelToJson(RegistrationResponseModel data) => json.encode(data.toJson());

class RegistrationResponseModel {
    String? remark;
    String? status;
    Message? message;
    Data? data;

    RegistrationResponseModel({
        this.remark,
        this.status,
        this.message,
        this.data,
    });

    factory RegistrationResponseModel.fromJson(Map<String, dynamic> json) => RegistrationResponseModel(
        remark: json["remark"],
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "remark": remark,
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
    };
}

class Data {
    String? accessToken;
    User? user;
    String? tokenType;

    Data({
        this.accessToken,
        this.user,
        this.tokenType,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        accessToken: json["access_token"],
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        tokenType: json["token_type"],
    );

    Map<String, dynamic> toJson() => {
        "access_token": accessToken,
        "user": user?.toJson(),
        "token_type": tokenType,
    };
}

class User {
    int? id;
    String? packageId;
    String? validity;
    String? telegramUsername;
    String? firstname;
    String? lastname;
    String? username;
    String? email;
    String? dialCode;
    String? countryCode;
    String? mobile;
    String? refBy;
    String? countryName;
    String? city;
    String? state;
    String? zip;
    String? address;
    String? status;
    String? ev;
    String? sv;
    String? profileComplete;
    String? verCodeSendAt;
    String? banReason;
    String? provider;
    String? providerId;
    String? createdAt;
    String? updatedAt;

    User({
        this.id,
        this.packageId,
        this.validity,
        this.telegramUsername,
        this.firstname,
        this.lastname,
        this.username,
        this.email,
        this.dialCode,
        this.countryCode,
        this.mobile,
        this.refBy,
        this.countryName,
        this.city,
        this.state,
        this.zip,
        this.address,
        this.status,
        this.ev,
        this.sv,
        this.profileComplete,
        this.verCodeSendAt,
        this.banReason,
        this.provider,
        this.providerId,
        this.createdAt,
        this.updatedAt,
    });

    factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        packageId: json["package_id"].toString(),
        validity: json["validity"].toString(),
        telegramUsername: json["telegram_username"].toString(),
        firstname: json["firstname"].toString(),
        lastname: json["lastname"].toString(),
        username: json["username"].toString(),
        email: json["email"].toString(),
        dialCode: json["dial_code"].toString(),
        countryCode: json["country_code"].toString(),
        mobile: json["mobile"].toString(),
        refBy: json["ref_by"].toString(),
        countryName: json["country_name"].toString(),
        city: json["city"].toString(),
        state: json["state"].toString(),
        zip: json["zip"].toString(),
        address: json["address"].toString(),
        status: json["status"].toString(),
        ev: json["ev"].toString(),
        sv: json["sv"].toString(),
        profileComplete: json["profile_complete"].toString(),
        verCodeSendAt: json["ver_code_send_at"].toString(),
        banReason: json["ban_reason"].toString(),
        provider: json["provider"].toString(),
        providerId: json["provider_id"].toString(),
        createdAt: json["created_at"] .toString(),
        updatedAt: json["updated_at"].toString(),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "package_id": packageId,
        "validity": validity,
        "telegram_username": telegramUsername,
        "firstname": firstname,
        "lastname": lastname,
        "username": username,
        "email": email,
        "dial_code": dialCode,
        "country_code": countryCode,
        "mobile": mobile,
        "ref_by": refBy,
        "country_name": countryName,
        "city": city,
        "state": state,
        "zip": zip,
        "address": address,
        "status": status,
        "ev": ev,
        "sv": sv,
        "profile_complete": profileComplete,
        "ver_code_send_at": verCodeSendAt,
        "ban_reason": banReason,
        "provider": provider,
        "provider_id": providerId,
        "created_at": createdAt,
        "updated_at": updatedAt,
    };
}


