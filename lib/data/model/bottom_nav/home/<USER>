// To parse this JSON data, do
//
//     final dashboardModel = dashboardModelFromJson(jsonString);

import 'dart:convert';

import '../../global/meassage_model.dart';

DashboardModel dashboardModelFromJson(String str) => DashboardModel.fromJson(json.decode(str));

String dashboardModelToJson(DashboardModel data) => json.encode(data.toJson());

class DashboardModel {
    String? remark;
    String? status;
    Message? message;
    Data? data;

    DashboardModel({
        this.remark,
        this.status,
        this.message,
        this.data,
    });

    factory DashboardModel.fromJson(Map<String, dynamic> json) => DashboardModel(
        remark: json["remark"],
        status: json["status"].toString(),
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "remark": remark,
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
    };
}

class Data {
    User? user;
    String? referralLink;
    String? totalTrx;
    String? totalSignal;
    String? totalReferral;
    String? totalDeposit;
    List<LatestSignal>? latestSignals;

    Data({
        this.user,
        this.referralLink,
        this.totalTrx,
        this.totalSignal,
        this.totalReferral,
        this.totalDeposit,
        this.latestSignals,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        referralLink: json["referral_link"].toString(),
        totalTrx: json["total_trx"].toString(),
        totalSignal: json["total_signal"].toString(),
        totalReferral: json["total_referral"].toString(),
        totalDeposit: json["total_deposit"].toString(),
        latestSignals: json["latest_signals"] == null ? [] : List<LatestSignal>.from(json["latest_signals"]!.map((x) => LatestSignal.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "user": user?.toJson(),
        "referral_link": referralLink,
        "total_trx": totalTrx,
        "total_signal": totalSignal,
        "total_referral": totalReferral,
        "total_deposit": totalDeposit,
        "latest_signals": latestSignals == null ? [] : List<dynamic>.from(latestSignals!.map((x) => x.toJson())),
    };
}

class LatestSignal {
    int? id;
    String? userId;
    String? signalId;
    String? createdAt;
    String? updatedAt;
    Signal? signal;

    LatestSignal({
        this.id,
        this.userId,
        this.signalId,
        this.createdAt,
        this.updatedAt,
        this.signal,
    });

    factory LatestSignal.fromJson(Map<String, dynamic> json) => LatestSignal(
        id: json["id"],
        userId: json["user_id"].toString(),
        signalId: json["signal_id"].toString(),
        createdAt: json["created_at"].toString(),
        updatedAt: json["updated_at"].toString() ,
        signal: json["signal"] == null ? null : Signal.fromJson(json["signal"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "signal_id": signalId,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "signal": signal?.toJson(),
    };
}

class Signal {
    int? id;
    List<String>? packageId;
    List<String>? sendVia;
    String? name;
    String? signal;
    String? minute;
    String? send;
    String? status;
    String? sendSignalAt;
    String? createdAt;
    String? updatedAt;

    Signal({
        this.id,
        this.packageId,
        this.sendVia,
        this.name,
        this.signal,
        this.minute,
        this.send,
        this.status,
        this.sendSignalAt,
        this.createdAt,
        this.updatedAt,
    });

    factory Signal.fromJson(Map<String, dynamic> json) => Signal(
        id: json["id"],
        packageId: json["package_id"] == null ? [] : List<String>.from(json["package_id"]!.map((x) => x)),
        sendVia: json["send_via"] == null ? [] : List<String>.from(json["send_via"]!.map((x) => x)),
        name: json["name"].toString(),
        signal: json["signal"].toString(),
        minute: json["minute"].toString(),
        send: json["send"].toString(),
        status: json["status"].toString(),
        sendSignalAt: json["send_signal_at"].toString(),
        createdAt: json["created_at"].toString(),
        updatedAt: json["updated_at"].toString(),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "package_id": packageId == null ? [] : List<dynamic>.from(packageId!.map((x) => x)),
        "send_via": sendVia == null ? [] : List<dynamic>.from(sendVia!.map((x) => x)),
        "name": name,
        "signal": signal,
        "minute": minute,
        "send": send,
        "status": status,
        "send_signal_at": sendSignalAt,
        "created_at": createdAt,
        "updated_at": updatedAt,
    };
}

class User {
    int? id;
    String? packageId;
    String? validity;
    String? telegramUsername;
    String? firstname;
    String? lastname;
    String? username;
    String? email;
    String? dialCode;
    String? countryCode;
    String? mobile;
    String? refBy;
    String? balance;
    String? countryName;
    String? city;
    String? state;
    String? zip;
    String? address;
    String? status;
    String? ev;
    String? sv;
    String? profileComplete;
    String? verCodeSendAt;
    String? banReason;
    String? provider;
    String? providerId;
    String? createdAt;
    String? updatedAt;
    Package? package;
    List<User>? referrals;

    User({
        this.id,
        this.packageId,
        this.validity,
        this.telegramUsername,
        this.firstname,
        this.lastname,
        this.username,
        this.email,
        this.dialCode,
        this.countryCode,
        this.mobile,
        this.refBy,
        this.countryName,
        this.city,
        this.state,
        this.zip,
          this.balance,
        this.address,
        this.status,
        this.ev,
        this.sv,
        this.profileComplete,
        this.verCodeSendAt,
        this.banReason,
        this.provider,
        this.providerId,
        this.createdAt,
        this.updatedAt,
        this.package,
        this.referrals,
    });

    factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        packageId: json["package_id"].toString(),
        validity: json["validity"].toString(),
        telegramUsername: json["telegram_username"].toString(),
        firstname: json["firstname"].toString(),
        lastname: json["lastname"].toString(),
        username: json["username"].toString(),
        email: json["email"].toString(),
        dialCode: json["dial_code"].toString(),
        countryCode: json["country_code"].toString(),
        mobile: json["mobile"].toString(),
        refBy: json["ref_by"].toString(),
        countryName: json["country_name"].toString(),
        city: json["city"].toString(),
        state: json["state"].toString(),
         balance: json["balance"].toString(),
        zip: json["zip"].toString(),
        address: json["address"].toString(),
        status: json["status"].toString(),
        ev: json["ev"].toString(),
        sv: json["sv"].toString(),
        profileComplete: json["profile_complete"].toString(),
        verCodeSendAt: json["ver_code_send_at"].toString(),
        banReason: json["ban_reason"].toString(),
        provider: json["provider"].toString(),
        providerId: json["provider_id"].toString(),
        createdAt: json["created_at"].toString(),
        updatedAt: json["updated_at"].toString(),
        package: json["package"] == null ? null : Package.fromJson(json["package"]),
        referrals: json["referrals"] == null ? [] : List<User>.from(json["referrals"]!.map((x) => User.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "package_id": packageId,
        "validity": validity,
        "telegram_username": telegramUsername,
        "firstname": firstname,
        "lastname": lastname,
        "username": username,
        "email": email,
        "dial_code": dialCode,
        "country_code": countryCode,
        "mobile": mobile,
        "ref_by": refBy,
        "balance": balance,
        "country_name": countryName,
        "city": city,
        "state": state,
        "zip": zip,
        "address": address,
        "status": status,
        "ev": ev,
        "sv": sv,
        "profile_complete": profileComplete,
        "ver_code_send_at": verCodeSendAt,
        "ban_reason": banReason,
        "provider": provider,
        "provider_id": providerId,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "package": package?.toJson(),
        "referrals": referrals == null ? [] : List<dynamic>.from(referrals!.map((x) => x.toJson())),
    };
}

class Package {
    int? id;
    String? name;
    String? price;
    String? validity;
    List<String>? features;
    String? status;
    String? createdAt;
    String? updatedAt;

    Package({
        this.id,
        this.name,
        this.price,
        this.validity,
        this.features,
        this.status,
        this.createdAt,
        this.updatedAt,
    });

    factory Package.fromJson(Map<String, dynamic> json) => Package(
        id: json["id"],
        name: json["name"].toString(),
        price: json["price"].toString(),
        validity: json["validity"].toString(),
        features: json["features"] == null ? [] : List<String>.from(json["features"]!.map((x) => x)),
        status: json["status"].toString(),
        createdAt: json["created_at"] .toString(),
        updatedAt: json["updated_at"] .toString(),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "price": price,
        "validity": validity,
        "features": features == null ? [] : List<dynamic>.from(features!.map((x) => x)),
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
    };
}


