
import '../global/meassage_model.dart';

class SignalModel {
  SignalModel({
      String? remark, 
      String? status, 
      Message? message,
      Signals? data,}){
    _remark = remark;
    _status = status;
    _message = message;
    _data = data;
}

  SignalModel.fromJson(dynamic json) {
    _remark = json['remark'];
    _status = json['status'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Signals.fromJson(json['data']) : null;
  }
  String? _remark;
  String? _status;
  Message? _message;
  Signals? _data;

  String? get remark => _remark;
  String? get status => _status;
  Message? get message => _message;
  Signals? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['remark'] = _remark;
    map['status'] = _status;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

class Signals {
  Signals({
      int? currentPage, 
      List<Signal>? data, 
      String? firstPageUrl, 
      int? from, 
      int? lastPage, 
      String? lastPageUrl, 
      List<String>? links, 
      String? nextPageUrl, 
      String? path, 
      int? perPage, 
      dynamic prevPageUrl, 
      int? to, 
      int? total,}){
    _currentPage = currentPage;
    _data = data;
    _firstPageUrl = firstPageUrl;
    _from = from;
    _lastPage = lastPage;
    _lastPageUrl = lastPageUrl;
    _links = links;
    _nextPageUrl = nextPageUrl;
    _path = path;
    _perPage = perPage;
    _prevPageUrl = prevPageUrl;
    _to = to;
    _total = total;
}

  Signals.fromJson(dynamic json) {
    _currentPage = int.tryParse(json['current_page'].toString());
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(Signal.fromJson(v));
      });
    }
    _firstPageUrl = json['first_page_url'];
    _from = int.tryParse(json['from'].toString());
    _lastPage = int.tryParse(json['last_page'].toString());
    _lastPageUrl = json['last_page_url'];
    if (json['links'] != null) {
      _links = List<String>.from(json['links'].map((x) => x));
    }
    _nextPageUrl = json['next_page_url'];
    _path = json['path'];
    _perPage = int.tryParse(json['per_page'].toString());
    _prevPageUrl = json['prev_page_url'];
    _to = int.tryParse(json['to'].toString());
    _total = int.tryParse(json['total'].toString());
  }
  int? _currentPage;
  List<Signal>? _data;
  String? _firstPageUrl;
  int? _from;
  int? _lastPage;
  String? _lastPageUrl;
  List<String>? _links;
  String? _nextPageUrl;
  String? _path;
  int? _perPage;
  dynamic _prevPageUrl;
  int? _to;
  int? _total;

  int? get currentPage => _currentPage;
  List<Signal>? get data => _data;
  String? get firstPageUrl => _firstPageUrl;
  int? get from => _from;
  int? get lastPage => _lastPage;
  String? get lastPageUrl => _lastPageUrl;
  List<String>? get links => _links;
  String? get nextPageUrl => _nextPageUrl;
  String? get path => _path;
  int? get perPage => _perPage;
  dynamic get prevPageUrl => _prevPageUrl;
  int? get to => _to;
  int? get total => _total;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['current_page'] = _currentPage;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    map['first_page_url'] = _firstPageUrl;
    map['from'] = _from;
    map['last_page'] = _lastPage;
    map['last_page_url'] = _lastPageUrl;
    if (_links != null) {
      map['links'] = _links;
    }
    map['next_page_url'] = _nextPageUrl;
    map['path'] = _path;
    map['per_page'] = _perPage;
    map['prev_page_url'] = _prevPageUrl;
    map['to'] = _to;
    map['total'] = _total;
    return map;
  }

}

class Links {
  Links({
      dynamic url, 
      String? label, 
      bool? active,}){
    _url = url;
    _label = label;
    _active = active;
}

  Links.fromJson(dynamic json) {
    _url = json['url'];
    _label = json['label'];
    _active = json['active'];
  }
  dynamic _url;
  String? _label;
  bool? _active;

  dynamic get url => _url;
  String? get label => _label;
  bool? get active => _active;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['url'] = _url;
    map['label'] = _label;
    map['active'] = _active;
    return map;
  }

}

class Signal {
  Signal({
      int? id, 
      String? symbol, 
      String? category, 
      List<String>? packageId, 
      List<String>? sendVia, 
      String? name, 
      String? signal, 
      String? signalType, 
      String? entryPrice, 
      String? currentPrice, 
      String? stopLoss, 
      String? pnl, 
      List<String>? targets, 
      String? currentTarget, 
      String? comment, 
      String? tradingPairId, 
      String? minute, 
      String? send, 
      String? status, 
      String? originType, 
      String? sendSignalAt, 
      String? createdAt, 
      String? updatedAt, 
      String? access,}){
    _id = id;
    _symbol = symbol;
    _category = category;
    _packageId = packageId;
    _sendVia = sendVia;
    _name = name;
    _signal = signal;
    _signalType = signalType;
    _entryPrice = entryPrice;
    _currentPrice = currentPrice;
    _stopLoss = stopLoss;
    _pnl = pnl;
    _targets = targets;
    _currentTarget = currentTarget;
    _comment = comment;
    _tradingPairId = tradingPairId;
    _minute = minute;
    _send = send;
    _status = status;
    _originType = originType;
    _sendSignalAt = sendSignalAt;
    _createdAt = createdAt;
    _updatedAt = updatedAt;
    _access = access;
}

  Signal.fromJson(dynamic json) {
    _id = json['id'];
    _symbol = json['symbol'];
    _category = json['category'];
    _packageId = json['package_id'] != null ? json['package_id'].cast<String>() : [];
    _sendVia = json['send_via'] != null ? json['send_via'].cast<String>() : [];
    _name = json['name'];
    _signal = json['signal'].toString();
    _signalType = json['signal_type'];
    _entryPrice = json['entry_price'];
    _currentPrice = json['current_price'];
    _stopLoss = json['stop_loss'];
    _pnl = json['pnl'];
    _targets = json['targets'] != null ? json['targets'].cast<String>() : [];
    _currentTarget = json['current_target'];
    _comment = json['comment'];
    _tradingPairId = json['trading_pair_id'];
    _minute = json['minute'].toString();
    _send = json['send'].toString();
    _status = json['status'].toString();
    _originType = json['origin_type'];
    _sendSignalAt = json['send_signal_at'];
    _createdAt = json['created_at'];
    _updatedAt = json['updated_at'];
    _access = json['access'];
  }
  int? _id;
  String? _symbol;
  String? _category;
  List<String>? _packageId;
  List<String>? _sendVia;
  String? _name;
  String? _signal;
  String? _signalType;
  String? _entryPrice;
  String? _currentPrice;
  String? _stopLoss;
  String? _pnl;
  List<String>? _targets;
  String? _currentTarget;
  String? _comment;
  String? _tradingPairId;
  String? _minute;
  String? _send;
  String? _status;
  String? _originType;
  String? _sendSignalAt;
  String? _createdAt;
  String? _updatedAt;
  String? _access;

  int? get id => _id;
  String? get symbol => _symbol;
  String? get category => _category;
  List<String>? get packageId => _packageId;
  List<String>? get sendVia => _sendVia;
  String? get name => _name;
  String? get signal => _signal;
  String? get signalType => _signalType;
  String? get entryPrice => _entryPrice;
  String? get currentPrice => _currentPrice;
  String? get stopLoss => _stopLoss;
  String? get pnl => _pnl;
  List<String>? get targets => _targets;
  String? get currentTarget => _currentTarget;
  String? get comment => _comment;
  String? get tradingPairId => _tradingPairId;
  String? get minute => _minute;
  String? get send => _send;
  String? get status => _status;
  String? get originType => _originType;
  String? get sendSignalAt => _sendSignalAt;
  String? get createdAt => _createdAt;
  String? get updatedAt => _updatedAt;
  String? get access => _access;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['symbol'] = _symbol;
    map['category'] = _category;
    map['package_id'] = _packageId;
    map['send_via'] = _sendVia;
    map['name'] = _name;
    map['signal'] = _signal;
    map['signal_type'] = _signalType;
    map['entry_price'] = _entryPrice;
    map['current_price'] = _currentPrice;
    map['stop_loss'] = _stopLoss;
    map['pnl'] = _pnl;
    map['targets'] = _targets;
    map['current_target'] = _currentTarget;
    map['comment'] = _comment;
    map['trading_pair_id'] = _tradingPairId;
    map['minute'] = _minute;
    map['send'] = _send;
    map['status'] = _status;
    map['origin_type'] = _originType;
    map['send_signal_at'] = _sendSignalAt;
    map['created_at'] = _createdAt;
    map['updated_at'] = _updatedAt;
    map['access'] = _access;
    return map;
  }

}