import '../auth/registration_response_model.dart';
import '../global/meassage_model.dart';

/// remark : "referrals"
/// status : "success"
/// message : {"success":["Referrals"]}
/// data : {"referral_link":"https://sohan.thesoftking.com/signal_lab?reference=username","referrals":{"current_page":1,"data":[{"id":37,"package_id":"0","validity":null,"telegram_username":"zijev","firstname":"<PERSON><PERSON>","lastname":"<PERSON>","username":"username3202","email":"<EMAIL>","country_code":"AF","mobile":"93124789","ref_by":"8","balance":"10.00000000","image":null,"address":{"country":"Afghanistan","address":"Rem non velit Nam ab","state":"Maxime facilis eveni","zip":"80424","city":"Fuga Quia irure bea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-25T18:28:14.000000Z","updated_at":"2022-07-25T18:49:54.000000Z"},{"id":36,"package_id":"0","validity":null,"telegram_username":"secere","firstname":"Rooney","lastname":"Richards`","username":"xolirecyqo","email":"<EMAIL>","country_code":"AF","mobile":"931234657896","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Odit adipisicing sun","state":"In et dolores quia i","zip":"57589","city":"Velit nesciunt odit"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T21:18:27.000000Z","updated_at":"2022-07-23T21:19:19.000000Z"},{"id":35,"package_id":"0","validity":null,"telegram_username":null,"firstname":"India","lastname":"Hickman","username":"wevebuqyw","email":"<EMAIL>","country_code":"KR","mobile":"827","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Autem velit sit il","city":"Et sed harum nobis e","state":"Sint animi quas vo","zip":"92090","country":"Republic of South Korea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"0","ver_code":"657773","ver_code_send_at":"2022-07-24T08:02:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T20:55:39.000000Z","updated_at":"2022-07-23T21:04:19.000000Z"},{"id":34,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Odysseus","lastname":"Schroeder","username":"admin420","email":"<EMAIL>","country_code":"AF","mobile":"9312349","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Fugiat dolor atque","state":"Ut in eos autem qua","zip":"58070","country":"Afghanistan","city":"Sapiente cupiditate"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:17:18.000000Z","updated_at":"2022-07-21T02:20:23.000000Z"},{"id":33,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Dahlia","lastname":"Patel","username":"username320","email":"<EMAIL>","country_code":"AF","mobile":"93123465789","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Veniam voluptate et","state":"Consequat Quos offi","zip":"42978","city":"In exercitationem co"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:11:15.000000Z","updated_at":"2022-07-21T02:11:31.000000Z"},{"id":32,"package_id":"0","validity":null,"telegram_username":"asdfasdf","firstname":"Meredith","lastname":"Savage","username":"username420","email":"<EMAIL>","country_code":"MH","mobile":"69252","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Voluptatibus molesti","state":"In eum aut nobis est","zip":"61331","country":"sdafasdf","city":"Velit est qui quis"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T00:42:18.000000Z","updated_at":"2022-07-24T23:51:50.000000Z"},{"id":31,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"username236","email":"<EMAIL>","country_code":"BD","mobile":"88058745455754","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"1","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-22T00:22:57.000000Z","updated_at":"2022-03-22T00:22:57.000000Z"},{"id":30,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Herrod","lastname":"Parrish","username":"kyvyqujyj","email":"<EMAIL>","country_code":"CO","mobile":"5736564684","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Colombia","address":"Vitae labore iure es","state":"Sunt nisi et enim vo","zip":"82702","city":"In vel aspernatur si"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-21T20:53:20.000000Z","updated_at":"2022-03-21T21:09:45.000000Z"},{"id":29,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Derek","lastname":"Baird","username":"wykyfucywe","email":"<EMAIL>","country_code":"KN","mobile":"18695946542145","ref_by":"8","balance":"200.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Saint Kitts and Nevis","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-20T15:41:54.000000Z","updated_at":"2022-03-29T22:57:30.000000Z"},{"id":28,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Shelly","lastname":"Morales","username":"fajavidi","email":"<EMAIL>","country_code":"DZ","mobile":"213Accusamus at et rati","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Algeria","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"210333","ver_code_send_at":"2022-03-17T03:09:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-16T16:09:58.000000Z","updated_at":"2022-03-20T14:07:58.000000Z"},{"id":27,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser5588","email":"<EMAIL>","country_code":"AF","mobile":"59154685458","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Afghanistan"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"994511","ver_code_send_at":"2022-03-06T05:53:38.000000Z","ts":"1","tv":"0","tsc":null,"ban_reason":null,"created_at":"2021-06-11T18:27:14.000000Z","updated_at":"2022-03-09T19:25:03.000000Z"},{"id":26,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject3","email":"<EMAIL>","country_code":"BD","mobile":"88001628071673","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T21:38:28.000000Z","updated_at":"2021-06-09T21:38:28.000000Z"},{"id":25,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject2","email":"<EMAIL>","country_code":"BD","mobile":"88001628071672","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:35:17.000000Z","updated_at":"2021-06-09T18:35:17.000000Z"},{"id":24,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Mosta","lastname":"fizz","username":"mostasproject","email":"<EMAIL>","country_code":"BD","mobile":"88001628071671","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:31:54.000000Z","updated_at":"2021-06-10T01:39:55.000000Z"},{"id":23,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"myname5587","email":"myname5587@myname5587.myname5587","country_code":"AI","mobile":"1264123456789","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Anguilla","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"467842","ver_code_send_at":"2021-06-09T06:15:34.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-08T19:15:33.000000Z","updated_at":"2021-06-08T19:15:34.000000Z"},{"id":22,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"Name","username":"testuser","email":"<EMAIL>","country_code":"AU","mobile":"6165463548554","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Australia"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-18T18:47:02.000000Z","updated_at":"2022-04-03T18:16:38.000000Z"},{"id":20,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username6","email":"<EMAIL>","country_code":"AD","mobile":"*********","ref_by":"8","balance":"10010.00000000","image":null,"address":{"address":"Bangladesh","city":"Dhaka","state":"Dhaka","zip":"1230","country":"Andorra"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"1","tv":"1","tsc":"FSLM3BZVLNPY7JYI","ban_reason":null,"created_at":"2021-05-09T19:27:04.000000Z","updated_at":"2021-05-17T18:23:15.000000Z"},{"id":19,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username5","email":"<EMAIL>","country_code":"AF","mobile":"9365465456454","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-09T19:24:07.000000Z","updated_at":"2021-05-09T19:24:14.000000Z"},{"id":18,"package_id":"0","validity":null,"telegram_username":null,"firstname":"User","lastname":"Name","username":"username99","email":"<EMAIL>","country_code":"AZ","mobile":"99496565654","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Bangladesh"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"404482","ver_code_send_at":"2021-04-28T00:41:42.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-27T13:41:42.000000Z","updated_at":"2021-05-04T02:53:22.000000Z"},{"id":17,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser55","email":"<EMAIL>","country_code":"BD","mobile":"93654545453","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"439780","ver_code_send_at":"2021-04-17T01:17:26.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-16T14:17:26.000000Z","updated_at":"2021-05-01T18:37:53.000000Z"}],"first_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=1","from":1,"last_page":2,"last_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=1","label":"1","active":true},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"2","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"Next &raquo;","active":false}],"next_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","path":"https://sohan.thesoftking.com/signal_lab/api/referrals","per_page":20,"prev_page_url":null,"to":20,"total":23}}

class ReferralModel {
  ReferralModel({
      String? remark, 
      String? status, 
      Message? message, 
      Data? data,}){
    _remark = remark;
    _status = status;
    _message = message;
    _data = data;
}

  ReferralModel.fromJson(dynamic json) {
    _remark = json['remark'];
    _status = json['status'];
    _message = json['message'] != null ? Message.fromJson(json['message']) : null;
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
  String? _remark;
  String? _status;
  Message? _message;
  Data? _data;

  String? get remark => _remark;
  String? get status => _status;
  Message? get message => _message;
  Data? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['remark'] = _remark;
    map['status'] = _status;
    if (_message != null) {
      map['message'] = _message?.toJson();
    }
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

/// referral_link : "https://sohan.thesoftking.com/signal_lab?reference=username"
/// referrals : {"current_page":1,"data":[{"id":37,"package_id":"0","validity":null,"telegram_username":"zijev","firstname":"Honorato","lastname":"Heath","username":"username3202","email":"<EMAIL>","country_code":"AF","mobile":"93124789","ref_by":"8","balance":"10.00000000","image":null,"address":{"country":"Afghanistan","address":"Rem non velit Nam ab","state":"Maxime facilis eveni","zip":"80424","city":"Fuga Quia irure bea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-25T18:28:14.000000Z","updated_at":"2022-07-25T18:49:54.000000Z"},{"id":36,"package_id":"0","validity":null,"telegram_username":"secere","firstname":"Rooney","lastname":"Richards`","username":"xolirecyqo","email":"<EMAIL>","country_code":"AF","mobile":"931234657896","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Odit adipisicing sun","state":"In et dolores quia i","zip":"57589","city":"Velit nesciunt odit"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T21:18:27.000000Z","updated_at":"2022-07-23T21:19:19.000000Z"},{"id":35,"package_id":"0","validity":null,"telegram_username":null,"firstname":"India","lastname":"Hickman","username":"wevebuqyw","email":"<EMAIL>","country_code":"KR","mobile":"827","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Autem velit sit il","city":"Et sed harum nobis e","state":"Sint animi quas vo","zip":"92090","country":"Republic of South Korea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"0","ver_code":"657773","ver_code_send_at":"2022-07-24T08:02:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T20:55:39.000000Z","updated_at":"2022-07-23T21:04:19.000000Z"},{"id":34,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Odysseus","lastname":"Schroeder","username":"admin420","email":"<EMAIL>","country_code":"AF","mobile":"9312349","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Fugiat dolor atque","state":"Ut in eos autem qua","zip":"58070","country":"Afghanistan","city":"Sapiente cupiditate"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:17:18.000000Z","updated_at":"2022-07-21T02:20:23.000000Z"},{"id":33,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Dahlia","lastname":"Patel","username":"username320","email":"<EMAIL>","country_code":"AF","mobile":"93123465789","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Veniam voluptate et","state":"Consequat Quos offi","zip":"42978","city":"In exercitationem co"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:11:15.000000Z","updated_at":"2022-07-21T02:11:31.000000Z"},{"id":32,"package_id":"0","validity":null,"telegram_username":"asdfasdf","firstname":"Meredith","lastname":"Savage","username":"username420","email":"<EMAIL>","country_code":"MH","mobile":"69252","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Voluptatibus molesti","state":"In eum aut nobis est","zip":"61331","country":"sdafasdf","city":"Velit est qui quis"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T00:42:18.000000Z","updated_at":"2022-07-24T23:51:50.000000Z"},{"id":31,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"username236","email":"<EMAIL>","country_code":"BD","mobile":"88058745455754","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"1","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-22T00:22:57.000000Z","updated_at":"2022-03-22T00:22:57.000000Z"},{"id":30,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Herrod","lastname":"Parrish","username":"kyvyqujyj","email":"<EMAIL>","country_code":"CO","mobile":"5736564684","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Colombia","address":"Vitae labore iure es","state":"Sunt nisi et enim vo","zip":"82702","city":"In vel aspernatur si"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-21T20:53:20.000000Z","updated_at":"2022-03-21T21:09:45.000000Z"},{"id":29,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Derek","lastname":"Baird","username":"wykyfucywe","email":"<EMAIL>","country_code":"KN","mobile":"18695946542145","ref_by":"8","balance":"200.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Saint Kitts and Nevis","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-20T15:41:54.000000Z","updated_at":"2022-03-29T22:57:30.000000Z"},{"id":28,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Shelly","lastname":"Morales","username":"fajavidi","email":"<EMAIL>","country_code":"DZ","mobile":"213Accusamus at et rati","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Algeria","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"210333","ver_code_send_at":"2022-03-17T03:09:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-16T16:09:58.000000Z","updated_at":"2022-03-20T14:07:58.000000Z"},{"id":27,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser5588","email":"<EMAIL>","country_code":"AF","mobile":"59154685458","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Afghanistan"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"994511","ver_code_send_at":"2022-03-06T05:53:38.000000Z","ts":"1","tv":"0","tsc":null,"ban_reason":null,"created_at":"2021-06-11T18:27:14.000000Z","updated_at":"2022-03-09T19:25:03.000000Z"},{"id":26,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject3","email":"<EMAIL>","country_code":"BD","mobile":"88001628071673","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T21:38:28.000000Z","updated_at":"2021-06-09T21:38:28.000000Z"},{"id":25,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject2","email":"<EMAIL>","country_code":"BD","mobile":"88001628071672","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:35:17.000000Z","updated_at":"2021-06-09T18:35:17.000000Z"},{"id":24,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Mosta","lastname":"fizz","username":"mostasproject","email":"<EMAIL>","country_code":"BD","mobile":"88001628071671","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:31:54.000000Z","updated_at":"2021-06-10T01:39:55.000000Z"},{"id":23,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"myname5587","email":"myname5587@myname5587.myname5587","country_code":"AI","mobile":"1264123456789","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Anguilla","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"467842","ver_code_send_at":"2021-06-09T06:15:34.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-08T19:15:33.000000Z","updated_at":"2021-06-08T19:15:34.000000Z"},{"id":22,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"Name","username":"testuser","email":"<EMAIL>","country_code":"AU","mobile":"6165463548554","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Australia"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-18T18:47:02.000000Z","updated_at":"2022-04-03T18:16:38.000000Z"},{"id":20,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username6","email":"<EMAIL>","country_code":"AD","mobile":"*********","ref_by":"8","balance":"10010.00000000","image":null,"address":{"address":"Bangladesh","city":"Dhaka","state":"Dhaka","zip":"1230","country":"Andorra"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"1","tv":"1","tsc":"FSLM3BZVLNPY7JYI","ban_reason":null,"created_at":"2021-05-09T19:27:04.000000Z","updated_at":"2021-05-17T18:23:15.000000Z"},{"id":19,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username5","email":"<EMAIL>","country_code":"AF","mobile":"9365465456454","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-09T19:24:07.000000Z","updated_at":"2021-05-09T19:24:14.000000Z"},{"id":18,"package_id":"0","validity":null,"telegram_username":null,"firstname":"User","lastname":"Name","username":"username99","email":"<EMAIL>","country_code":"AZ","mobile":"99496565654","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Bangladesh"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"404482","ver_code_send_at":"2021-04-28T00:41:42.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-27T13:41:42.000000Z","updated_at":"2021-05-04T02:53:22.000000Z"},{"id":17,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser55","email":"<EMAIL>","country_code":"BD","mobile":"93654545453","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"439780","ver_code_send_at":"2021-04-17T01:17:26.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-16T14:17:26.000000Z","updated_at":"2021-05-01T18:37:53.000000Z"}],"first_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=1","from":1,"last_page":2,"last_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=1","label":"1","active":true},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"2","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"Next &raquo;","active":false}],"next_page_url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","path":"https://sohan.thesoftking.com/signal_lab/api/referrals","per_page":20,"prev_page_url":null,"to":20,"total":23}

class Data {
  Data({
      String? referralLink, 
      Referrals? referrals,}){
    _referralLink = referralLink;
    _referrals = referrals;
}

  Data.fromJson(dynamic json) {
    _referralLink = json['referral_link'];
    _referrals = json['referrals'] != null ? Referrals.fromJson(json['referrals']) : null;
  }
  String? _referralLink;
  Referrals? _referrals;

  String? get referralLink => _referralLink;
  Referrals? get referrals => _referrals;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['referral_link'] = _referralLink;
    if (_referrals != null) {
      map['referrals'] = _referrals?.toJson();
    }
    return map;
  }

}

/// current_page : 1
/// data : [{"id":37,"package_id":"0","validity":null,"telegram_username":"zijev","firstname":"Honorato","lastname":"Heath","username":"username3202","email":"<EMAIL>","country_code":"AF","mobile":"93124789","ref_by":"8","balance":"10.00000000","image":null,"address":{"country":"Afghanistan","address":"Rem non velit Nam ab","state":"Maxime facilis eveni","zip":"80424","city":"Fuga Quia irure bea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-25T18:28:14.000000Z","updated_at":"2022-07-25T18:49:54.000000Z"},{"id":36,"package_id":"0","validity":null,"telegram_username":"secere","firstname":"Rooney","lastname":"Richards`","username":"xolirecyqo","email":"<EMAIL>","country_code":"AF","mobile":"931234657896","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Odit adipisicing sun","state":"In et dolores quia i","zip":"57589","city":"Velit nesciunt odit"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T21:18:27.000000Z","updated_at":"2022-07-23T21:19:19.000000Z"},{"id":35,"package_id":"0","validity":null,"telegram_username":null,"firstname":"India","lastname":"Hickman","username":"wevebuqyw","email":"<EMAIL>","country_code":"KR","mobile":"827","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Autem velit sit il","city":"Et sed harum nobis e","state":"Sint animi quas vo","zip":"92090","country":"Republic of South Korea"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"0","ver_code":"657773","ver_code_send_at":"2022-07-24T08:02:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-23T20:55:39.000000Z","updated_at":"2022-07-23T21:04:19.000000Z"},{"id":34,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Odysseus","lastname":"Schroeder","username":"admin420","email":"<EMAIL>","country_code":"AF","mobile":"9312349","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Fugiat dolor atque","state":"Ut in eos autem qua","zip":"58070","country":"Afghanistan","city":"Sapiente cupiditate"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:17:18.000000Z","updated_at":"2022-07-21T02:20:23.000000Z"},{"id":33,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Dahlia","lastname":"Patel","username":"username320","email":"<EMAIL>","country_code":"AF","mobile":"93123465789","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Afghanistan","address":"Veniam voluptate et","state":"Consequat Quos offi","zip":"42978","city":"In exercitationem co"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T02:11:15.000000Z","updated_at":"2022-07-21T02:11:31.000000Z"},{"id":32,"package_id":"0","validity":null,"telegram_username":"asdfasdf","firstname":"Meredith","lastname":"Savage","username":"username420","email":"<EMAIL>","country_code":"MH","mobile":"69252","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"Voluptatibus molesti","state":"In eum aut nobis est","zip":"61331","country":"sdafasdf","city":"Velit est qui quis"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-07-21T00:42:18.000000Z","updated_at":"2022-07-24T23:51:50.000000Z"},{"id":31,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"username236","email":"<EMAIL>","country_code":"BD","mobile":"88058745455754","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"1","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-22T00:22:57.000000Z","updated_at":"2022-03-22T00:22:57.000000Z"},{"id":30,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Herrod","lastname":"Parrish","username":"kyvyqujyj","email":"<EMAIL>","country_code":"CO","mobile":"5736564684","ref_by":"8","balance":"0.00000000","image":null,"address":{"country":"Colombia","address":"Vitae labore iure es","state":"Sunt nisi et enim vo","zip":"82702","city":"In vel aspernatur si"},"status":"1","kyc_data":null,"kv":"1","ev":"1","sv":"1","reg_step":"1","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-21T20:53:20.000000Z","updated_at":"2022-03-21T21:09:45.000000Z"},{"id":29,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Derek","lastname":"Baird","username":"wykyfucywe","email":"<EMAIL>","country_code":"KN","mobile":"18695946542145","ref_by":"8","balance":"200.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Saint Kitts and Nevis","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-20T15:41:54.000000Z","updated_at":"2022-03-29T22:57:30.000000Z"},{"id":28,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Shelly","lastname":"Morales","username":"fajavidi","email":"<EMAIL>","country_code":"DZ","mobile":"213Accusamus at et rati","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Algeria","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"210333","ver_code_send_at":"2022-03-17T03:09:59.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2022-03-16T16:09:58.000000Z","updated_at":"2022-03-20T14:07:58.000000Z"},{"id":27,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser5588","email":"<EMAIL>","country_code":"AF","mobile":"59154685458","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Afghanistan"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":"994511","ver_code_send_at":"2022-03-06T05:53:38.000000Z","ts":"1","tv":"0","tsc":null,"ban_reason":null,"created_at":"2021-06-11T18:27:14.000000Z","updated_at":"2022-03-09T19:25:03.000000Z"},{"id":26,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject3","email":"<EMAIL>","country_code":"BD","mobile":"88001628071673","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T21:38:28.000000Z","updated_at":"2021-06-09T21:38:28.000000Z"},{"id":25,"package_id":"0","validity":null,"telegram_username":null,"firstname":null,"lastname":null,"username":"mostasproject2","email":"<EMAIL>","country_code":"BD","mobile":"88001628071672","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:35:17.000000Z","updated_at":"2021-06-09T18:35:17.000000Z"},{"id":24,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Mosta","lastname":"fizz","username":"mostasproject","email":"<EMAIL>","country_code":"BD","mobile":"88001628071671","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Bangladesh","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-09T18:31:54.000000Z","updated_at":"2021-06-10T01:39:55.000000Z"},{"id":23,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"myname5587","email":"myname5587@myname5587.myname5587","country_code":"AI","mobile":"1264123456789","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Anguilla","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"467842","ver_code_send_at":"2021-06-09T06:15:34.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-06-08T19:15:33.000000Z","updated_at":"2021-06-08T19:15:34.000000Z"},{"id":22,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"Name","username":"testuser","email":"<EMAIL>","country_code":"AU","mobile":"6165463548554","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Australia"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-18T18:47:02.000000Z","updated_at":"2022-04-03T18:16:38.000000Z"},{"id":20,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username6","email":"<EMAIL>","country_code":"AD","mobile":"*********","ref_by":"8","balance":"10010.00000000","image":null,"address":{"address":"Bangladesh","city":"Dhaka","state":"Dhaka","zip":"1230","country":"Andorra"},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"1","tv":"1","tsc":"FSLM3BZVLNPY7JYI","ban_reason":null,"created_at":"2021-05-09T19:27:04.000000Z","updated_at":"2021-05-17T18:23:15.000000Z"},{"id":19,"package_id":"0","validity":null,"telegram_username":null,"firstname":"My","lastname":"Name","username":"username5","email":"<EMAIL>","country_code":"AF","mobile":"9365465456454","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"1","sv":"1","reg_step":"0","ver_code":null,"ver_code_send_at":null,"ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-05-09T19:24:07.000000Z","updated_at":"2021-05-09T19:24:14.000000Z"},{"id":18,"package_id":"0","validity":null,"telegram_username":null,"firstname":"User","lastname":"Name","username":"username99","email":"<EMAIL>","country_code":"AZ","mobile":"99496565654","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":null,"city":null,"state":null,"zip":null,"country":"Bangladesh"},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"404482","ver_code_send_at":"2021-04-28T00:41:42.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-27T13:41:42.000000Z","updated_at":"2021-05-04T02:53:22.000000Z"},{"id":17,"package_id":"0","validity":null,"telegram_username":null,"firstname":"Test","lastname":"User","username":"testuser55","email":"<EMAIL>","country_code":"BD","mobile":"93654545453","ref_by":"8","balance":"0.00000000","image":null,"address":{"address":"","state":"","zip":"","country":"Afghanistan","city":""},"status":"1","kyc_data":null,"kv":"0","ev":"0","sv":"1","reg_step":"0","ver_code":"439780","ver_code_send_at":"2021-04-17T01:17:26.000000Z","ts":"0","tv":"1","tsc":null,"ban_reason":null,"created_at":"2021-04-16T14:17:26.000000Z","updated_at":"2021-05-01T18:37:53.000000Z"}]
/// first_page_url : "https://sohan.thesoftking.com/signal_lab/api/referrals?page=1"
/// from : 1
/// last_page : 2
/// last_page_url : "https://sohan.thesoftking.com/signal_lab/api/referrals?page=2"
/// links : [{"url":null,"label":"&laquo; Previous","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=1","label":"1","active":true},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"2","active":false},{"url":"https://sohan.thesoftking.com/signal_lab/api/referrals?page=2","label":"Next &raquo;","active":false}]
/// next_page_url : "https://sohan.thesoftking.com/signal_lab/api/referrals?page=2"
/// path : "https://sohan.thesoftking.com/signal_lab/api/referrals"
/// per_page : 20
/// prev_page_url : null
/// to : 20
/// total : 23

class Referrals {
  Referrals({
      int? currentPage, 
      List<ReferralData>? data,
      String? firstPageUrl, 
      int? from, 
      int? lastPage, 
      String? lastPageUrl, 
      List<Links>? links, 
      String? nextPageUrl, 
      String? path, 
      int? perPage, 
      dynamic prevPageUrl, 
      int? to, 
      int? total,}){
    _currentPage = currentPage;
    _data = data;
    _firstPageUrl = firstPageUrl;
    _from = from;
    _lastPage = lastPage;
    _lastPageUrl = lastPageUrl;
    _links = links;
    _nextPageUrl = nextPageUrl;
    _path = path;
    _perPage = perPage;
    _prevPageUrl = prevPageUrl;
    _to = to;
    _total = total;
}

  Referrals.fromJson(dynamic json) {
    _currentPage = json['current_page'];
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(ReferralData.fromJson(v));
      });
    }
    _firstPageUrl = json['first_page_url'];
    _from = json['from'];
    _lastPage = json['last_page'];
    _lastPageUrl = json['last_page_url'];
    if (json['links'] != null) {
      _links = [];
      json['links'].forEach((v) {
        _links?.add(Links.fromJson(v));
      });
    }
    _nextPageUrl = json['next_page_url'];
    _path = json['path'];
    _perPage = json['per_page'];
    _prevPageUrl = json['prev_page_url'];
    _to = json['to'];
    _total = json['total'];
  }
  int? _currentPage;
  List<ReferralData>? _data;
  String? _firstPageUrl;
  int? _from;
  int? _lastPage;
  String? _lastPageUrl;
  List<Links>? _links;
  String? _nextPageUrl;
  String? _path;
  int? _perPage;
  dynamic _prevPageUrl;
  int? _to;
  int? _total;

  int? get currentPage => _currentPage;
  List<ReferralData>? get data => _data;
  String? get firstPageUrl => _firstPageUrl;
  int? get from => _from;
  int? get lastPage => _lastPage;
  String? get lastPageUrl => _lastPageUrl;
  List<Links>? get links => _links;
  String? get nextPageUrl => _nextPageUrl;
  String? get path => _path;
  int? get perPage => _perPage;
  dynamic get prevPageUrl => _prevPageUrl;
  int? get to => _to;
  int? get total => _total;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['current_page'] = _currentPage;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    map['first_page_url'] = _firstPageUrl;
    map['from'] = _from;
    map['last_page'] = _lastPage;
    map['last_page_url'] = _lastPageUrl;
    if (_links != null) {
      map['links'] = _links?.map((v) => v.toJson()).toList();
    }
    map['next_page_url'] = _nextPageUrl;
    map['path'] = _path;
    map['per_page'] = _perPage;
    map['prev_page_url'] = _prevPageUrl;
    map['to'] = _to;
    map['total'] = _total;
    return map;
  }

}

/// url : null
/// label : "&laquo; Previous"
/// active : false

class Links {
  Links({
      dynamic url, 
      String? label, 
      bool? active,}){
    _url = url;
    _label = label;
    _active = active;
}

  Links.fromJson(dynamic json) {
    _url = json['url'];
    _label = json['label'];
    _active = json['active'];
  }
  dynamic _url;
  String? _label;
  bool? _active;

  dynamic get url => _url;
  String? get label => _label;
  bool? get active => _active;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['url'] = _url;
    map['label'] = _label;
    map['active'] = _active;
    return map;
  }

}

class ReferralData {
  ReferralData({
      int? id, 
      String? packageId, 
      dynamic validity, 
      String? telegramUsername, 
      String? firstname, 
      String? lastname, 
      String? username, 
      String? email, 
      String? countryCode, 
      String? mobile, 
      String? refBy, 
      String? balance, 
      dynamic image, 
      
      String? status, 
      dynamic kycData, 
      String? kv, 
      String? ev, 
      String? sv, 
      String? regStep, 
      dynamic verCode, 
      dynamic verCodeSendAt, 
      String? ts, 
      String? tv, 
      
      String? createdAt, 
      String? updatedAt,}){
    _id = id;
    _packageId = packageId;
    _validity = validity;
    _telegramUsername = telegramUsername;
    _firstname = firstname;
    _lastname = lastname;
    _username = username;
    _email = email;
    _countryCode = countryCode;
    _mobile = mobile;
    _refBy = refBy;
    _balance = balance;
    _image = image;
   
    _status = status;
    _kycData = kycData;
    _kv = kv;
    _ev = ev;
    _sv = sv;
    _regStep = regStep;
    _verCode = verCode;
    _verCodeSendAt = verCodeSendAt;
    _ts = ts;
    _tv = tv;
   
    _createdAt = createdAt;
    _updatedAt = updatedAt;
}

  ReferralData.fromJson(dynamic json) {
    _id = json['id'];
    _packageId = json['package_id'].toString();
    _validity = json['validity'].toString();
    _telegramUsername = json['telegram_username'];
    _firstname = json['firstname'];
    _lastname = json['lastname'];
    _username = json['username'];
    _email = json['email'];
    _countryCode = json['country_code'];
    _mobile = json['mobile'];
    _refBy = json['ref_by'].toString();
    _balance = json['balance'].toString();
    _image = json['image'];
   
    _status = json['status'].toString();


    _verCode = json['ver_code'];
    _verCodeSendAt = json['ver_code_send_at'];

   
    _createdAt = json['created_at'];
    _updatedAt = json['updated_at'];
  }
  int? _id;
  String? _packageId;
  dynamic _validity;
  String? _telegramUsername;
  String? _firstname;
  String? _lastname;
  String? _username;
  String? _email;
  String? _countryCode;
  String? _mobile;
  String? _refBy;
  String? _balance;
  dynamic _image;
 
  String? _status;
  dynamic _kycData;
  String? _kv;
  String? _ev;
  String? _sv;
  String? _regStep;
  dynamic _verCode;
  dynamic _verCodeSendAt;
  String? _ts;
  String? _tv;

  String? _createdAt;
  String? _updatedAt;

  int? get id => _id;
  String? get packageId => _packageId;
  dynamic get validity => _validity;
  String? get telegramUsername => _telegramUsername;
  String? get firstname => _firstname;
  String? get lastname => _lastname;
  String? get username => _username;
  String? get email => _email;
  String? get countryCode => _countryCode;
  String? get mobile => _mobile;
  String? get refBy => _refBy;
  String? get balance => _balance;
  dynamic get image => _image;
  
  String? get status => _status;
  dynamic get kycData => _kycData;
  String? get kv => _kv;
  String? get ev => _ev;
  String? get sv => _sv;
  String? get regStep => _regStep;
  dynamic get verCode => _verCode;
  dynamic get verCodeSendAt => _verCodeSendAt;
  String? get ts => _ts;
  String? get tv => _tv;

  String? get createdAt => _createdAt;
  String? get updatedAt => _updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['package_id'] = _packageId;
    map['validity'] = _validity;
    map['telegram_username'] = _telegramUsername;
    map['firstname'] = _firstname;
    map['lastname'] = _lastname;
    map['username'] = _username;
    map['email'] = _email;
    map['country_code'] = _countryCode;
    map['mobile'] = _mobile;
    map['ref_by'] = _refBy;
    map['balance'] = _balance;
    map['image'] = _image;
  
    map['status'] = _status;
    map['kyc_data'] = _kycData;
    map['kv'] = _kv;
    map['ev'] = _ev;
    map['sv'] = _sv;
    map['reg_step'] = _regStep;
    map['ver_code'] = _verCode;
    map['ver_code_send_at'] = _verCodeSendAt;
    map['ts'] = _ts;
    map['tv'] = _tv;

    map['created_at'] = _createdAt;
    map['updated_at'] = _updatedAt;
    return map;
  }

}

