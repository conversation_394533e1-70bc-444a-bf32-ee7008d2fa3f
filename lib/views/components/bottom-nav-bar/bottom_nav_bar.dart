import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/my_images.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:signal_lab/core/utils/style.dart';
import '../../../core/utils/my_color.dart';

class CustomBottomNavBar extends StatefulWidget {
  final int currentIndex;
  const CustomBottomNavBar({Key? key,required this.currentIndex}) : super(key: key);

  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar> {
  final autoSizeGroup = AutoSizeGroup();
  var bottomNavIndex = 0;

  // List<String> iconList = [MyImages.homeIcon, MyImages.priceIcon, MyImages.transaction, MyImages.menuIcon];//old-1
  List<String> iconList = [MyImages.homeIcon, MyImages.signalsIcon, MyImages.vip, MyImages.learnIcon, MyImages.userProfileIcon];
  List<String> labelList = [MyStrings.home, MyStrings.signals, MyStrings.vip, MyStrings.learning, MyStrings.profile];

  @override
  void initState() {
    bottomNavIndex = widget.currentIndex;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBottomNavigationBar.builder(
      // safeAreaBottom: false,
      elevation: 0,
      height: 65,
      itemCount: iconList.length,
      tabBuilder: (int index, bool isActive) {
        final color = isActive ? MyColor.primaryColor : MyColor.colorWhite;
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              iconList[index],
              height: 20, width: 20,
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(isActive? MyColor.primaryColor : MyColor.colorWhite, BlendMode.srcIn)
              // color: isActive? MyColor.primaryColor : MyColor.colorWhite,
            ),
            const SizedBox(height: 5),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: AutoSizeText(
                labelList[index],
                maxLines: 1,
                style: interNormalSmall.copyWith(color: color),
                group: autoSizeGroup,
              ),
            )
          ],
        );
      },
      backgroundColor: MyColor.appBarBgColor,
      splashColor: MyColor.colorWhite,
      splashSpeedInMilliseconds: 300,
      notchSmoothness: NotchSmoothness.defaultEdge,
      gapLocation: GapLocation.none,
      leftCornerRadius: 0,
      rightCornerRadius: 0,
      onTap: (index) {
        _onTap(index);
      },
      activeIndex: bottomNavIndex,
    );
  }


  void _onTap(int index) {

    if (index == 0) {
      if (!(widget.currentIndex == 0)) {
        Get.offAndToNamed(RouteHelper.homeScreen);
      }
    }

    else if (index == 1) {
      if (!(widget.currentIndex == 1)) {
        Get.offAndToNamed(RouteHelper.pricingScreen);
      }
    }

    else if (index == 2) {
      if (!(widget.currentIndex == 2)) {
        Get.offAndToNamed(RouteHelper.transactionHistoryScreen);
      }
    }

    else if (index == 3) {
      if (!(widget.currentIndex == 3)) {
        Get.offAndToNamed(RouteHelper.learnScreen);
      }
    }

    else if (index == 4) {
      if (!(widget.currentIndex == 4)) {
        Get.offAndToNamed(RouteHelper.menuScreen);
      }
    }

  }
}




  


