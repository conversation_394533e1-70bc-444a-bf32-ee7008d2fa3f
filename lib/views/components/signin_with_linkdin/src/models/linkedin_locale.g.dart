// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'linkedin_locale.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LinkedInLocale _$LinkedInLocaleFromJson(Map<String, dynamic> json) =>
    LinkedInLocale(
      country: json['country'] as String?,
      language: json['language'] as String?,
    );

Map<String, dynamic> _$LinkedInLocaleToJson(LinkedInLocale instance) =>
    <String, dynamic>{
      'country': instance.country,
      'language': instance.language,
    };
