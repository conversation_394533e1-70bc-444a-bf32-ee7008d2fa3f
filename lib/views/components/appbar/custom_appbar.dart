import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/dimensions.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/style.dart';

import '../../../data/services/api_service.dart';




class CustomAppBar extends StatefulWidget implements PreferredSizeWidget{

  final String title;
  final bool isShowBackBtn;
  final bool isShowActionBtn;
  final Color bgColor;
  final bool isTitleCenter;
  final bool fromAuth;
  final bool fromSignalScreen;
  final bool isProfileCompleted;
  final VoidCallback? actionPress;
  final IconData icon;
    final List<Widget>? action;

  const CustomAppBar({Key? key,
    this.isProfileCompleted=false,
    this.fromAuth=false,
    this.isTitleCenter=false,
    this.bgColor = MyColor.appBarBgColor,
    this.isShowBackBtn=true,
    this.actionPress,
    this.icon = Icons.search,
    this.isShowActionBtn = false,
    this.fromSignalScreen = false,
        this.action,
    required this.title}) : super(key: key);

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size(double.maxFinite, 50);
}

class _CustomAppBarState extends State<CustomAppBar> {

  late ApiClient client;

  @override
  void initState() {
   client = Get.put(ApiClient(sharedPreferences: Get.find()));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.isShowBackBtn?AppBar(
      elevation: 0,
      leading:widget.isShowBackBtn?
      InkWell(
          onTap: ()async{
        if(widget.fromAuth){
          await client.clearSharedData();
          Get.offAllNamed(RouteHelper.signInScreen);
        }else if(widget.isProfileCompleted){
          await client.clearSharedData();
          Get.offAllNamed(RouteHelper.signInScreen);
        }
        else{
          String previousRoute=Get.previousRoute;
          if(previousRoute == '/splash-screen'){
            Get.offAndToNamed(RouteHelper.homeScreen);
          }else{

              Get.back();

          }
        }
      },child: const Icon(Icons.arrow_back,color: MyColor.colorWhite, size: 22)):const SizedBox.shrink(),
      backgroundColor: widget.bgColor,
      title: Text(widget.title,style: interNormalDefault.copyWith(color: MyColor.colorWhite)),
      centerTitle: widget.isTitleCenter,
      actions:widget.action?? [
        widget.isShowActionBtn?Padding(
          padding: const EdgeInsets.only(right: Dimensions.space15),
          child: GestureDetector(
            onTap: widget.actionPress,
            child: Container(
              alignment: Alignment.center,
              height: 30, width: 30,
              decoration: BoxDecoration(shape: BoxShape.circle, color: MyColor.colorWhite.withOpacity(0.04)),
              child:  Icon(widget.icon, color: MyColor.colorWhite, size: 18)
            ),
          ),
        ):const SizedBox()
      ],
    ):AppBar(
      elevation: 0,
      backgroundColor: widget.bgColor,
      title:Text(
        widget.title,
        style: interNormalDefault.copyWith(color: MyColor.colorWhite),),
      automaticallyImplyLeading: false,
      actions:widget.action?? [
        widget.isShowActionBtn?Padding(
          padding: const EdgeInsets.only(right: Dimensions.space15),
          child: GestureDetector(
            onTap: widget.actionPress,
            child: Container(
                alignment: Alignment.center,
                height: 30, width: 30,
                decoration: BoxDecoration(shape: BoxShape.circle, color: MyColor.colorWhite.withOpacity(0.04)),
                child:  Icon(widget.icon, color: MyColor.colorWhite, size: 18)
            ),
          ),
        ):const SizedBox()
      ],
    );
  }


}
