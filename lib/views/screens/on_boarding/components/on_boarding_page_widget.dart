import 'package:flutter/material.dart';

import '../../../../core/utils/style.dart';
import '../../../../data/controller/on_boarding/mode_on_boarding.dart';

class OnBoardingPageWidget extends StatelessWidget {
  const OnBoardingPageWidget({super.key, required this.model});

  final OnBoardingModel model;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      padding: const EdgeInsets.all(20),
      color: model.bgColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Image(image: AssetImage(model.image), height: size.height * 0.45),
          Column(children: [Text(model.title, style: interNormalOverLarge.copyWith(color: Colors.black)), Text(model.subTitle, style: interNormalDefault.copyWith(color: Colors.black45, fontWeight: FontWeight.w600), textAlign: TextAlign.center)]),
          Text(model.counterText, style: interNormalDefaultLarge.copyWith(color: Colors.black, fontWeight: FontWeight.bold)),
          const SizedBox(height: 80.0),
        ],
      ),
    );
  }
}