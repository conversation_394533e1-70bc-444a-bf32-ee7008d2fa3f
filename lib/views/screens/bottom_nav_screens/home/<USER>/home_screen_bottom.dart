import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/bottom_nav/home/<USER>';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';

class HomeScreenBottom extends StatelessWidget {

  const HomeScreenBottom({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return GetBuilder<HomeController>(
      builder: (controller) => Container(
        width: double.infinity,
        color: MyColor.cardBgColor,
        alignment: Alignment.center,
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              MyStrings.referralLink,
              textAlign: TextAlign.center,
              style: interNormalSmall.copyWith(color: MyColor.colorWhite),
            ),
            const SizedBox(height: 10),

            SizedBox(
              height: 40, width: double.infinity,
              child: DottedBorder(
                // color: MyColor.colorWhite.withOpacity(0.22),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                       Flexible(child:  Text(
                         controller.referralLink,
                         textAlign: TextAlign.center,
                         style: interNormalSmall.copyWith(color: MyColor.colorWhite),
                         overflow: TextOverflow.ellipsis,
                       )),
                        const SizedBox(width: 20),
                        GestureDetector(
                          onTap: (){
                            Clipboard.setData(ClipboardData(text: controller.referralLink));
                            MySnackbar.success(msg: [MyStrings.referralLinkCopied]);
                          },
                          child: const Icon(Icons.copy, color: MyColor.primaryColor, size: 16),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
