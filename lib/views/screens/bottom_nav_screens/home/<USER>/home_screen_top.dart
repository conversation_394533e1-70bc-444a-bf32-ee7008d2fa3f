import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/helper/my_converter.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_images.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/bottom_nav/home/<USER>';

class HomeScreenTop extends StatelessWidget {
  const HomeScreenTop({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      builder: (controller) => Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 15),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: MyColor.cardBgColor
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    height: 40,
                    width: 40,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(.04),
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(MyImages.totalBalanceIcon, height: 20, width: 20)
                ),
                const SizedBox(width: 20),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${controller.currencySymbol} ${MyConverter.twoDecimalPlaceFixedWithoutRounding(controller.balance)}",
                      style: interNormalDefaultLarge.copyWith(color: MyColor.primaryColor, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 5),

                    Text(
                      MyStrings.totalBalance,
                      style: interNormalSmall.copyWith(color: MyColor.colorWhite),
                    ),
                  ],
                )
              ],
            ),
            GestureDetector(
              onTap: (){
                Get.toNamed(RouteHelper.depositNowScreen);
              },
              child: Container(
                  height: 30,
                  width: 30,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: MyColor.colorWhite.withOpacity(0.04),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.add, color: MyColor.colorWhite, size: 15)
              ),
            ),
        ],
      ),
    ));
  }
}
