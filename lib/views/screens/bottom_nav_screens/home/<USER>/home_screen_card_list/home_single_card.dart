import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/style.dart';

class HomeSingleCard extends StatelessWidget {

  final String firstText;
  final String secondText;
  final String imageUrl;
  final VoidCallback press;
  final bool isSvg;

  const HomeSingleCard({Key? key,
  required this.firstText,
  required this.secondText,
  required this.imageUrl,
  required this.press,
  this.isSvg = true
  }) : super(key: key);


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: press,
      child: Container(
        width: 150,
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        decoration: BoxDecoration(
            color: MyColor.cardBgColor,
            borderRadius: BorderRadius.circular(8)
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                height: 40, width: 40,
                alignment: Alignment.center,
                decoration:BoxDecoration(
                  color: Colors.white.withOpacity(.04),
                  shape: BoxShape.circle,
                ),
                child: isSvg?SvgPicture.asset(imageUrl, height: 20, width: 20, color: MyColor.primaryColor):
                    Image.asset(imageUrl,height: 20,width: 20,color: MyColor.primaryColor,)
            ),
            const SizedBox(height: 10),
            Text(
              firstText,
              style: interNormalDefaultLarge.copyWith(color: MyColor.colorWhite, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Text(
              secondText,
              style: interNormalSmall.copyWith(color: MyColor.colorWhite),
            ),
          ],
        ),
      ),
    );
  }
}
