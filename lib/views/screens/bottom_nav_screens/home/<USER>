import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/dimensions.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/bottom_nav/home/<USER>';
import 'package:signal_lab/data/repo/bottom_nav/home/<USER>';
import 'package:signal_lab/data/services/api_service.dart';
import 'package:signal_lab/views/components/bottom-nav-bar/bottom_nav_bar.dart';
import 'package:signal_lab/views/components/custom_loader.dart';
import 'package:signal_lab/views/components/will_pop_widget.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>/home_screen_bottom.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>/home_screen_signal_list.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>/home_screen_top.dart';

import 'widget/home_screen_card_list/home_screen_card_list.dart';

class HomeScreen extends StatefulWidget {

  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {

  @override
  void initState() {

    Get.put(ApiClient(sharedPreferences: Get.find()));
    Get.put(HomeRepo(apiClient: Get.find()));
    final controller = Get.put(HomeController(dashboardRepo: Get.find()));

    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      controller.getDashboardData();
    });

  }

  @override
  Widget build(BuildContext context) {
    return WillPopWidget(
      nextRoute: '',
      child: SafeArea(
        child: Scaffold(
          backgroundColor: MyColor.backgroundColor,
          body: GetBuilder<HomeController>(
            builder: (controller) {
              return controller.isLoading ? const Center(
                child: CustomLoader(),
              ) :
              SingleChildScrollView(
                padding: const EdgeInsets.all(Dimensions.space15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const HomeScreenTop(),
                    const SizedBox(height: 10),
                    const HomeScreenCardList(),
                    const SizedBox(height: 17),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          MyStrings.latestSignal,
                          style: interNormalDefault.copyWith(color: MyColor.colorWhite, fontWeight: FontWeight.w600),
                        ),

                        GestureDetector(
                          onTap: () => Get.toNamed(RouteHelper.signalScreen),
                          child: Container(
                            padding: const EdgeInsets.all(Dimensions.space10),
                            alignment: Alignment.center,
                            child: Text(
                              MyStrings.allSignal,
                              textAlign: TextAlign.center,
                              style: interNormalSmall.copyWith(color: MyColor.colorGrey, fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const HomeScreenSignalList(),
                    const SizedBox(height: 20),
                    const HomeScreenBottom()
                  ],
                ),
              );
            }
          ),
          bottomNavigationBar: const CustomBottomNavBar(currentIndex: 0),
        ),
      ),
    );
  }
}
