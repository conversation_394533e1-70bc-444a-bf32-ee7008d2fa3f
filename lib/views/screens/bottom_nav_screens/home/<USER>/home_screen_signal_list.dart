import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:signal_lab/core/helper/date_converter.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_images.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/bottom_nav/home/<USER>';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>/no_data_widget.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>/signal_list_bottom_sheet.dart';

class HomeScreenSignalList extends StatelessWidget {

  const HomeScreenSignalList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return GetBuilder<HomeController>(
      builder: (controller){
        return controller.signalsList.isEmpty? const NoDataWidget():ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.signalsList.length,
          separatorBuilder: (context, index) => const SizedBox(height: 10),
          itemBuilder: (context, index) => GestureDetector(
            onTap: (){
              SignalListBottomSheet.showBottomSheet(
                  context,
                  signalName: "${controller.signalsList[index].signal?.name}",
                  time: DateConverter. getSubtractTime(controller.signalsList[index].createdAt??''),
                  signalDetails: "${controller.signalsList[index].signal?.signal}"
              );
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              decoration: BoxDecoration(
                  color: MyColor.cardBgColor,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 50, width: 50,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.04), borderRadius: BorderRadius.circular(10)
                        ),
                        child: Text(
                          "${index + 1}".padLeft(2,"0"),
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(color: MyColor.textColor, fontWeight: FontWeight.w500, fontSize: 17),
                        ),
                      ),

                      Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 150,
                              child: Text(
                                "${controller.signalsList[index].signal?.name}",
                                style: interSemiBoldSmall,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                SvgPicture.asset(MyImages.clockIcon, color: MyColor.colorWhite.withOpacity(0.8), height: 15, width: 15),
                                const SizedBox(width: 5),
                                Text(
                                  DateConverter. isoStringToLocalDateOnly(controller.signalsList[index].createdAt??''),
                                  style: interNormalSmall.copyWith(color: MyColor.colorWhite.withOpacity(0.8)),
                                )
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  ),

                  Container(
                    height: 30,
                    width: 30,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(color: MyColor.circleBorder, width: 1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.arrow_forward_ios, color: Colors.white.withOpacity(.7), size: 10),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
