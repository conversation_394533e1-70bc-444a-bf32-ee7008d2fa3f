import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/dimensions.dart';
import 'package:signal_lab/core/utils/my_images.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/menu/menu_controller.dart' as menu;
import 'package:signal_lab/data/repo/menu_repo/menu_repo.dart';
import 'package:signal_lab/data/services/api_service.dart';
import 'package:signal_lab/push_notification_service.dart';
import 'package:signal_lab/views/components/bottom-nav-bar/bottom_nav_bar.dart';
import 'package:signal_lab/views/components/bottom-sheet/custom_bottom_sheet.dart';
import 'package:signal_lab/views/components/divider/custom_divider.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/menu/widget/delete_account_bottom_sheet_body.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/menu/widget/menu_top_card.dart';

import 'widget/icon_with_text_widget.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({Key? key}) : super(key: key);

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  @override
  void initState() {
    Get.put(ApiClient(sharedPreferences: Get.find()));
    Get.put(MenuRepo(apiClient: Get.find()));
    Get.put(menu.MenuController(repo: Get.find()));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: MyColor.backgroundColor,
        appBar: AppBar(
          title: Text(MyStrings.userMenu, style: interNormalDefaultLarge.copyWith(color: MyColor.colorWhite)),
          elevation: 0,
          automaticallyImplyLeading: false,
          backgroundColor: MyColor.appBarBgColor,
        ),
        body: GetBuilder<menu.MenuController>(
            builder: (controller) => SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      children: [
                        const MenuTopCard(),
                        const SizedBox(height: 20),
                        Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                Get.toNamed(RouteHelper.privacyPolicyScreen);
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                                  child: const Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [IconWithTextWidget(icon: MyImages.privacy, text: MyStrings.privacyPolicy), CustomDivider(space: Dimensions.space20)],
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  Get.toNamed(RouteHelper.allTicketScreen);
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                                  child: const Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [IconWithTextWidget(icon: MyImages.support, text: MyStrings.supportTicket), CustomDivider(space: Dimensions.space20)],
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  Get.toNamed(RouteHelper.faqScreen);
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                                  child: const Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [IconWithTextWidget(icon: MyImages.faq, text: MyStrings.faq), CustomDivider(space: Dimensions.space20)],
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  CustomBottomSheet(
                                    bgColor: MyColor.cardBgColor,
                                    isNeedMargin: true,
                                    child: const DeleteAccountBottomsheetBody(),
                                  ).customBottomSheet(context);
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                                  child: const Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [IconWithTextWidget(icon: MyImages.deleteAccount, text: MyStrings.deleteAccount), CustomDivider(space: Dimensions.space20)],
                                  ),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  controller.logout();
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(5)),
                                  child: controller.logoutLoading
                                      ? const Center(
                                          child: SizedBox(
                                              height: 25,
                                              width: 25,
                                              child: CircularProgressIndicator(
                                                color: MyColor.primaryColor,
                                              )),
                                        )
                                      : const IconWithTextWidget(icon: MyImages.signOutIcon, text: MyStrings.signOut),
                                ),
                              )
                            ],
                          ),
                        ),
                        const SizedBox(height: 15),
                      ],
                    ),
                  ),
                )),
        bottomNavigationBar: const CustomBottomNavBar(currentIndex: 3),
      ),
    );
  }
}
