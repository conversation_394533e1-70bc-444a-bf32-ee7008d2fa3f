import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_images.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/views/components/buttons/rounded_button.dart';
import 'package:signal_lab/views/components/divider/custom_divider.dart';

class PlanCard extends StatelessWidget {

  final String cardName, price, packageTime;
  final List<String> featureList;
  final VoidCallback onPressed;

  const PlanCard({
    Key? key,
    required this.cardName,
    required this.price,
    required this.packageTime,
    required this.featureList,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(color: MyColor.cardBgColor, borderRadius: BorderRadius.circular(8)),
      child:Theme(
        data: Theme.of(context).copyWith(
            scrollbarTheme: ScrollbarThemeData(
              thumbColor: MaterialStateProperty.all(Colors.red),
            )),
        child: Column(
          children: [
            const SizedBox(height: 20),
            Text(cardName, textAlign: TextAlign.center, style: interNormalDefault.copyWith(color: MyColor.colorWhite)),
            const SizedBox(height: 5),
            RichText(
              text: TextSpan(
                  children: [
                    TextSpan(text: price, style: interNormalOverLarge.copyWith(color: MyColor.colorWhite, fontWeight: FontWeight.w600)),
                    TextSpan(text: ' / ', style: interNormalDefault.copyWith(color: MyColor.colorWhite, fontWeight: FontWeight.w600)),
                    TextSpan(text: packageTime, style: interNormalDefault.copyWith(color: MyColor.colorWhite, fontWeight: FontWeight.w600))
                  ]
              ),
            ),
            const CustomDivider(),
            Expanded(
              child: Scrollbar(
                child: ListView.separated(
                 itemCount: featureList.length,
                 padding: const EdgeInsets.symmetric(horizontal: 25),
                 physics: const BouncingScrollPhysics(),
                 scrollDirection: Axis.vertical,
                 shrinkWrap: true,
                 separatorBuilder: (context, index) => const SizedBox(height: 30),
                 itemBuilder: (context, index) => Row(
                   mainAxisAlignment: MainAxisAlignment.start,
                   children: [
                     SvgPicture.asset(MyImages.checkImage, height: 12, width: 12),
                     const SizedBox(width: 10),
                  Expanded(
                       child: Text(featureList[index], style: interNormalDefault.copyWith(color: MyColor.colorWhite)),
                     )
                   ],
                 ),
                ),
              ),),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: RoundedButton(
                  color: MyColor.primaryColor,
                  text: MyStrings.choosePlan,
                  press: onPressed
              ),

            ),
            const SizedBox(height: 25),
          ],
        ),
      )
    );
  }
}
