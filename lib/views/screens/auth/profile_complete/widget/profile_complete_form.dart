import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/account/profile_complete/profile_complete_controller.dart';
import 'package:signal_lab/views/components/buttons/rounded_button.dart';
import 'package:signal_lab/views/components/buttons/rounded_loading_button.dart';
import 'package:signal_lab/views/components/text_field/another_custom_text_field.dart';
import 'package:signal_lab/views/components/text_field/custom_text_form_field.dart';
import 'package:signal_lab/views/components/text_field/custom_text_form_field2.dart';

class ProfileCompleteForm extends StatefulWidget {

  const ProfileCompleteForm({Key? key,}) : super(key: key);

  @override
  State<ProfileCompleteForm> createState() => _ProfileCompleteFormState();
}

class _ProfileCompleteFormState extends State<ProfileCompleteForm> {

  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileCompleteController>(
      builder: (controller) => Form(
        child: Column(
          children: [

            CustomTextFormField(
                labelText: MyStrings.userName,
                onChanged: (value){
                  return;
                },
                focusNode: controller.usernameFocusNode,
                controller: controller.usernameController
            ),
            const SizedBox(height: 25),
            AnotherCustomTextField(
              onTap: () {
                showModalBottomSheet(isScrollControlled:true,backgroundColor:Colors.transparent,context: context, builder: (BuildContext context){
                  return Container(
                    height: MediaQuery.of(context).size.height*.8,
                    padding: const EdgeInsets.all(20),
                    decoration: const BoxDecoration(
                        color: MyColor.cardBgColor,
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(25),topRight: Radius.circular(25))
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 8),
                        Center(
                          child: Container(
                            height: 5,
                            width: 100,
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.white.withOpacity(.04),
                            ),

                          ),
                        ),
                        const SizedBox(height: 15,),
                        Flexible(
                          child: ListView.builder(
                              itemCount:controller.countryList.length,
                              shrinkWrap: true,
                              physics: const BouncingScrollPhysics(),
                              itemBuilder: (context,index){
                                return Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: (){
                                      controller.countryController.text = controller.countryList[index].country??'';
                                      controller.setCountryNameAndCode(controller.countryList[index].country??'',
                                          controller.countryList[index].countryCode??'', controller.countryList[index].dialCode??'');

                                      Navigator.pop(context);

                                      FocusScopeNode currentFocus = FocusScope.of(context);
                                      if (!currentFocus.hasPrimaryFocus) {
                                        currentFocus.unfocus();
                                      }

                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(15),
                                      margin: const EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(.04),
                                        borderRadius: BorderRadius.circular(4),
                                       // border: Border.all(color:MyColor.colorHint),
                                      ),
                                      child: Text(
                                          '+${controller.countryList[index].dialCode}  ${controller.countryList[index].country}',
                                          style: interNormalSmall.copyWith(color: MyColor.colorWhite)
                                      ),
                                    ),
                                  ),
                                );
                              }
                          ),
                        )
                      ],
                    ),
                  );
                });
              },
              child: CustomTextFormField2(
                labelText: MyStrings.selectCountry,
                focusNode: controller.countryFocusNode,
                inputType: TextInputType.phone,
                isEnabled: false,
                fillColor: MyColor.colorWhite,
                isShowSuffixIcon: true,
                isCountryPicker: true,
                isIcon: true,
                controller: controller.countryController,
                suffixIconUrl: '',
                hintText: MyStrings.selectCountry,
                onChanged: (value) {
                  return;
                },
              ),
            ),
            const SizedBox(height: 25),
            controller.countryName == null ? const SizedBox() : Column(
              children: [
                AnotherCustomTextField(
                  isShowSuffixView: true,
                  isShowBorder: false,
                  prefixWidgetValue: '+${controller.mobileCode?.tr}',
                  child: CustomTextFormField2(
                    labelText: MyStrings.phoneNumber,
                    controller: controller.mobileNoController,
                    focusNode: controller.mobileFocusNode,
                    inputType: TextInputType.phone,
                    onChanged: (value) {},
                  ),
                  onTap: (){},
                ),

                const SizedBox(height: 25),
              ],
            ),

           
           

            CustomTextFormField(
                labelText: MyStrings.address,
                onChanged: (value){
                  return;
                },
                focusNode: controller.addressFocusNode,
                controller: controller.addressController
            ),
            const SizedBox(height: 25),

            CustomTextFormField(
                labelText: MyStrings.state,
                onChanged: (value){
                  return ;
                },
                focusNode: controller.stateFocusNode,
                controller: controller.stateController
            ),
            const SizedBox(height: 25),

            CustomTextFormField(
                labelText: MyStrings.zipCode,
                onChanged: (value){
                  return;
                },
                focusNode: controller.zipCodeFocusNode,
                controller: controller.zipCodeController
            ),
            const SizedBox(height: 25),

            CustomTextFormField(
                labelText: MyStrings.city,
                onChanged: (value){
                  return ;
                },
                focusNode: controller.cityFocusNode,
                controller: controller.cityController,
            ),

            const SizedBox(height: 35),


            controller.submitLoading?
            const RoundedLoadingBtn() :
            RoundedButton(
                text: MyStrings.updateProfile,
                press: (){
                  controller.updateProfile();
                },
            )
          ],
        ),
      ),
    );
  }
}
