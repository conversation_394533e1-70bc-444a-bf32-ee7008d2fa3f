import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signal_lab/data/controller/profile/user_profile_controller.dart';
import 'package:signal_lab/data/repo/profile/user_profile_repo.dart';
import 'package:signal_lab/views/components/appbar/custom_appbar.dart';
import 'package:signal_lab/views/components/custom_loader.dart';
import 'package:signal_lab/views/screens/auth/two_factor/two_factor_setup_screen/sections/two_factor_disable_section.dart';
import 'package:signal_lab/views/screens/auth/two_factor/two_factor_setup_screen/sections/two_factor_enable_section.dart';
import '../../../../../../core/utils/my_color.dart';
import '../../../../../../core/utils/my_strings.dart';
import '../../../../../../data/controller/auth/two_factor_controller.dart';
import '../../../../../../data/repo/auth/two_factor_repo.dart';
import '../../../../../../data/services/api_service.dart';

class TwoFactorSetupScreen extends StatefulWidget {
  const TwoFactorSetupScreen({super.key});

  @override
  State<TwoFactorSetupScreen> createState() => _TwoFactorSetupScreenState();
}

class _TwoFactorSetupScreenState extends State<TwoFactorSetupScreen> {
  @override
  void initState() {
    Get.put(ApiClient(sharedPreferences: Get.find()));
    Get.put(TwoFactorRepo(apiClient: Get.find()));
    final controller = Get.put(TwoFactorController(repo: Get.find()));
    Get.put(UserProfileRepo(apiClient: Get.find()));
    final pcontroller = Get.put(UserProfileController(userProfileRepo: Get.find()));
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      pcontroller.loadProfileInfo();
      controller.get2FaCode();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TwoFactorController>(builder: (controller) {
      return GetBuilder<UserProfileController>(builder: (profileController) {
        return Scaffold(
          backgroundColor: MyColor.backgroundColor,
          appBar: CustomAppBar(
            isShowBackBtn: true,
            title: MyStrings.twoFactorAuth.tr,
          ),
          body: controller.isLoading || profileController.isLoading
              ? const Center(child:  CustomLoader())
              : profileController.user2faIsOne == false
                  ? const TwoFactorEnableSection()
                  : const TwoFactorDisableSection(),
        );
      });
    });
  }
}
