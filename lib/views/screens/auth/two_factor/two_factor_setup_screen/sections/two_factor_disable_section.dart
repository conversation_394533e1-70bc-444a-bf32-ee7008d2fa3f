import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/auth/two_factor_controller.dart';
import 'package:signal_lab/views/components/buttons/rounded_button.dart';
import 'package:signal_lab/views/components/buttons/rounded_loading_button.dart';
import 'package:signal_lab/views/components/divider/custom_divider.dart';

import '../../../../../../../core/utils/dimensions.dart';
import '../../../../../../../core/utils/my_color.dart';
import '../../../../../../../core/utils/my_strings.dart';

class TwoFactorDisableSection extends StatelessWidget {
  const TwoFactorDisableSection({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TwoFactorController>(builder: (twoFactorController) {
      return Column(
        children: [
          Container(
              width: MediaQuery.of(context).size.width,
              margin: const EdgeInsets.symmetric(vertical: Dimensions.space15, horizontal: Dimensions.space15),
              padding: const EdgeInsets.symmetric(vertical: Dimensions.space15, horizontal: Dimensions.space15),
              decoration: BoxDecoration(color: MyColor.getCardBgColor(), borderRadius: BorderRadius.circular(10)),
              child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: [
                Center(
                  child: Text(
                    MyStrings.disable2Fa.tr,
                    style: interSemiBoldLarge,
                  ),
                ),
                const CustomDivider(),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * .07),
                  child: Text( MyStrings.twoFactorMsg.tr,  textAlign: TextAlign.center, style: interNormalDefault.copyWith(color: MyColor.getGreyColor())),
                ),
                const SizedBox(height: Dimensions.space50),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: Dimensions.space30),
                  child: PinCodeTextField(
                    appContext: context,
                    pastedTextStyle: interNormalDefault.copyWith(color: MyColor.getGreyColor()),
                    length: 6,
                    textStyle: interNormalDefault.copyWith(color: MyColor.getGreyColor()),
                    obscureText: false,
                    obscuringCharacter: '*',
                    blinkWhenObscuring: false,
                    animationType: AnimationType.fade,
                    pinTheme: PinTheme(
                        shape: PinCodeFieldShape.box,
                        borderWidth: 1,
                        borderRadius: BorderRadius.circular(5),
                        fieldHeight: 40,
                        fieldWidth: 40,
                        inactiveColor: MyColor.getTextFieldDisableBorder(),
                        inactiveFillColor: MyColor.getTransparentColor(),
                        activeFillColor: MyColor.getTransparentColor(),
                        activeColor: MyColor.getPrimaryColor(),
                        selectedFillColor: MyColor.getTransparentColor(),
                        selectedColor: MyColor.getPrimaryColor()),
                    cursorColor: MyColor.getWhiteColor(),
                    animationDuration: const Duration(milliseconds: 100),
                    enableActiveFill: true,
                    keyboardType: TextInputType.number,
                    beforeTextPaste: (text) {
                      return true;
                    },
                    onChanged: (value) {
                      twoFactorController.currentText = value;
                      twoFactorController.update();
                    },
                  ),
                ),
                const SizedBox(height: Dimensions.space30),
                twoFactorController.submitLoading
                    ? const RoundedLoadingBtn()
                    : RoundedButton(
                        press: () {
                          twoFactorController.disable2fa(twoFactorController.currentText);
                        },
                        text: MyStrings.submit.tr,
                      ),
                const SizedBox(height: Dimensions.space30),
              ])),
        ],
      );
    });
  }
}
