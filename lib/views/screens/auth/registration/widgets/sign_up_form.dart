import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:signal_lab/core/route/route.dart';
import 'package:signal_lab/core/utils/my_color.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/core/utils/style.dart';
import 'package:signal_lab/data/controller/auth/registration/registration_controller.dart';
import 'package:signal_lab/views/components/buttons/rounded_button.dart';
import 'package:signal_lab/views/components/buttons/rounded_loading_button.dart';
import 'package:signal_lab/views/components/text_field/custom_text_form_field.dart';
import 'package:signal_lab/views/screens/auth/login/widget/social_login_section.dart';
import 'package:signal_lab/views/screens/auth/registration/widgets/validation_widget.dart';

class SignUpForm extends StatefulWidget {
  const SignUpForm({Key? key}) : super(key: key);

  @override
  State<SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<SignUpForm> {
  final formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RegistrationController>(
      builder: (controller) => Form(
        key: formKey,
        child: Column(
          children: [
            CustomTextFormField(
              labelText: MyStrings.firstName,
              onChanged: (value) {},
              controller: controller.fNameController,
              focusNode: controller.firstNameFocusNode,
              validator: (value) {
                if (value!.isEmpty) {
                  return MyStrings.enterYourFirstName;
                } else {
                  return null;
                }
              },
            ),
            const SizedBox(height: 25),
            CustomTextFormField(
              labelText: MyStrings.lastName,
              onChanged: (value) {},
              controller: controller.lNameController,
              focusNode: controller.lastNameFocusNode,
              validator: (value) {
                if (value!.isEmpty) {
                  return MyStrings.enterYourLastName;
                } else {
                  return null;
                }
              },
            ),
            const SizedBox(height: 25),
            CustomTextFormField(
              labelText: MyStrings.emailAddress,
              controller: controller.emailController,
              focusNode: controller.emailFocusNode,
              textInputType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isEmpty) {
                  return MyStrings.enterYourEmail;
                } else if (!MyStrings.emailValidatorRegExp.hasMatch(value ?? '')) {
                  return MyStrings.enterAValidEmail;
                } else {
                  return null;
                }
              },
              onChanged: (value) {},
            ),
            const SizedBox(height: 25),
            Visibility(
                visible: controller.hasPasswordFocus && controller.checkPasswordStrength,
                child: ValidationWidget(
                  list: controller.passwordValidationRulse,
                )),
            Focus(
              onFocusChange: (hasFocus) {
                controller.changePasswordFocus(hasFocus);
              },
              child: CustomTextFormField(
                isShowSuffixIcon: true,
                isPassword: true,
                labelText: MyStrings.password,
                controller: controller.passwordController,
                focusNode: controller.passwordFocusNode,
                validator: (value) {
                  return controller.validatePassword(value ?? '');
                },
                onChanged: (value) {
                  if (controller.checkPasswordStrength) {
                    controller.updateValidationList(value);
                  }
                },
              ),
            ),
            const SizedBox(height: 25),
            CustomTextFormField(
              labelText: MyStrings.confirmPassword,
              isShowSuffixIcon: true,
              isPassword: true,
              controller: controller.cPasswordController,
              focusNode: controller.confirmPasswordFocusNode,
              validator: (value) {
                if (controller.passwordController.text.toLowerCase() != controller.cPasswordController.text.toLowerCase()) {
                  return MyStrings.kMatchPassError;
                } else {
                  return null;
                }
              },
              onChanged: (String? value) {},
            ),
            const SizedBox(height: 25),
            CustomTextFormField(
              labelText: MyStrings.referralCode,
              controller: controller.refferalCodeController,
              focusNode: controller.refferalCodeFocusNode,
              onChanged: (String? value) {},
            ),
            const SizedBox(height: 25),
            controller.needAgree
                ? Row(
                    children: [
                      Checkbox(
                        checkColor: MyColor.textColor,
                        fillColor: WidgetStateProperty.all(MyColor.transparentColor),
                        activeColor: MyColor.primaryColor,
                        value: controller.agreeTC,
                        onChanged: (bool? value) {
                          controller.updateAgreeTC();
                        },
                      ),
                      Row(
                        children: [
                          Text(MyStrings.iAgreeWith, style: interNormalDefault.copyWith(color: MyColor.colorWhite)),
                          const SizedBox(width: 3),
                          GestureDetector(
                            onTap: () {
                              Get.toNamed(RouteHelper.privacyPolicyScreen);
                            },
                            child: Text(MyStrings.policies.toLowerCase(), style: GoogleFonts.inter(color: MyColor.primaryColor, decoration: TextDecoration.underline, decorationColor: MyColor.primaryColor)),
                          ),
                          const SizedBox(width: 3),
                        ],
                      ),
                    ],
                  )
                : const SizedBox(),
            const SizedBox(height: 25),
            controller.submitLoading
                ? const RoundedLoadingBtn()
                : RoundedButton(
                    text: MyStrings.signUp,
                    press: () {
                      if (formKey.currentState!.validate()) {
                        controller.signUpUser();
                      }
                    },
                  ),
            const SocialLoginSection(),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(MyStrings.alreadyAccount, style: interNormalDefault.copyWith(color: MyColor.colorWhite)),
                TextButton(
                  onPressed: () {
                    Get.offAllNamed(RouteHelper.signInScreen);
                  },
                  child: Text(MyStrings.signInNow, style: interNormalDefault.copyWith(color: MyColor.primaryColor)),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
