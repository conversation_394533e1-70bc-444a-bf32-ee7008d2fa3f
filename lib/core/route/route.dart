import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signal_lab/core/helper/shared_pref_helper.dart';
import 'package:signal_lab/push_notification_service.dart';
import 'package:signal_lab/views/components/preview_image.dart';
import 'package:signal_lab/views/screens/account/change-password/change_password_screen.dart';
import 'package:signal_lab/views/screens/auth/email_verification/email_verification_screen.dart';
import 'package:signal_lab/views/screens/auth/forget_password/forget_password/forget_password.dart';
import 'package:signal_lab/views/screens/auth/forget_password/reset_pass/reset_pass_screen.dart';
import 'package:signal_lab/views/screens/auth/forget_password/verify_forget_password_code_screen/verify_forget_pass_code.dart';
import 'package:signal_lab/views/screens/auth/profile_complete/profile_complete_screen.dart';
import 'package:signal_lab/views/screens/auth/sms_verification/sms_verification_screen.dart';
import 'package:signal_lab/views/screens/auth/two_factor/two_factor_setup_screen/two_factor_setup_screen.dart';
import 'package:signal_lab/views/screens/auth/two_factor/two_factor_verification_screen/two_factor_verification_screen.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/home/<USER>';
import 'package:signal_lab/views/screens/bottom_nav_screens/menu/menu_screen.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/pricing-plan/plan_screen.dart';
import 'package:signal_lab/views/screens/bottom_nav_screens/transaction-history/transaction_history_screen.dart';
import 'package:signal_lab/views/screens/deposit/deposit-history/deposit_history_screen.dart';
import 'package:signal_lab/views/screens/deposit/deposit-now/deposit_now_screen.dart';
import 'package:signal_lab/views/screens/deposit/deposit-now/deposit_web_view.dart';
import 'package:signal_lab/views/screens/faq/faq_screen.dart';
import 'package:signal_lab/views/screens/language/language_screen.dart';
import 'package:signal_lab/views/screens/on_boarding/on_boarding_screen.dart';
import 'package:signal_lab/views/screens/privacy-policy/privacy_policy_screen.dart';
import 'package:signal_lab/views/screens/auth/login/sign_in_screen.dart';
import 'package:signal_lab/views/screens/auth/registration/sign_up_screen.dart';
import 'package:signal_lab/views/screens/profile/edit-profile/edit_profile_screen.dart';
import 'package:signal_lab/views/screens/profile/user-profile/user_profile_screen.dart';
import 'package:signal_lab/views/screens/referral/referral_screen.dart';
import 'package:signal_lab/views/screens/signal/signal_screen.dart';
import 'package:signal_lab/views/screens/splash/splash_screen.dart';
import 'package:signal_lab/views/screens/ticket/all_ticket_screen/all_ticket_screen.dart';
import 'package:signal_lab/views/screens/ticket/new_ticket_screen/new_ticket_screen.dart';
import 'package:signal_lab/views/screens/ticket/ticket_details_screen/ticket_details_screen.dart';
import '../../data/model/user/user.dart';
import '../../views/screens/bottom_nav_screens/learn/learn_screen.dart';

class RouteHelper {
  static const String splashScreen = "/splash_screen";
  static const String onBoardScreen = "/onboard_screen";
  static const String signInScreen = "/login_screen";
  static const String signUpScreen = "/sign_up_screen";
  static const String bottomNav = "/bottom_nav_bar";
  static const String homeScreen = "/home_screen";
  static const String onBoardingScreen = "/onboarding_screen";
  static const String pricingScreen = "/pricing_screen";
  static const String notificationScreen = "/notification_screen";
  static const String menuScreen = "/menu_screen";
  static const String learnScreen = "/learn_screen";
  static const String userProfileScreen = "/user_profile_screen";
  static const String editProfileScreen = "/edit_profile_screen";
  static const String changePasswordScreen = "/change_password_screen";
  static const String transactionHistoryScreen = "/transaction_history_screen";
  static const String depositHistoryScreen = "/deposit_history_screen";
  static const String depositNowScreen = "/deposit_now_screen";
  static const String privacyPolicyScreen = "/privacy_policy_screen";
  static const String depositWebScreen = "/deposit_web_view_screen";
  static const String signalScreen = "/signal_screen";
  static const String forgotPasswordScreen = "/forget_password_screen";
  static const String verifyPassCodeScreen = '/verify-pass-code';
  static const String resetPasswordScreen = '/reset-pass';
  static const String profileCompleteScreen = "/profile_complete_screen";
  static const String emailVerificationScreen = "/email_verification_screen";
  static const String smsVerificationScreen = "/sms_verification_screen";
  static const String referralScreen = "/referral_screen";
  // static const String twoFactorScreen = "/two-factor-screen";
 // static const String twoFactorSetupScreen = "/two-factor-setup-screen";
  static const String faqScreen = "/faq-screen";
  static const String newTicketScreen = '/new_ticket_screen';
  static const String allTicketScreen = '/all_ticket_screen';
  static const String previewImageScreen = "/preview-image-screen";
  static const String ticketDetailsScreen = '/ticket_details_screen';
  static const String languageScreen = "/languages_screen";

  static List<GetPage> routes = [
    GetPage(name: splashScreen, page: () => const SplashScreen()),
    GetPage(name: onBoardingScreen, page: () => const OnBoardingScreen()),
    GetPage(name: signInScreen, page: () => const SignInScreen()),
    GetPage(name: signUpScreen, page: () => const SignUpScreen()),
    GetPage(name: homeScreen, page: () => const HomeScreen()),
    GetPage(name: languageScreen, page: () => const LanguageScreen()),
    GetPage(name: pricingScreen, page: () => const PlanScreen()),
    GetPage(name: menuScreen, page: () => const MenuScreen()),
    GetPage(name: learnScreen, page: () => const LearnScreen()),
    GetPage(name: userProfileScreen, page: () => const UserProfileScreen()),
    GetPage(name: editProfileScreen, page: () => const EditProfileScreen()),
    GetPage(name: changePasswordScreen, page: () => const ChangePasswordScreen()),
    GetPage(name: transactionHistoryScreen, page: () => const TransactionHistoryScreen()),
    GetPage(name: depositHistoryScreen, page: () => const DepositHistoryScreen()),
    GetPage(name: depositNowScreen, page: () => const DepositNowScreen()),
    GetPage(name: privacyPolicyScreen, page: () => const PrivacyScreen()),
    GetPage(name: depositWebScreen, page: () => DepositWebView(redirectUrl: Get.arguments)),
    GetPage(name: signalScreen, page: () => const SignalScreen()),
    GetPage(name: emailVerificationScreen, page: () => const EmailVerificationScreen()),
    //GetPage(name: twoFactorSetupScreen, page: () => const TwoFactorSetupScreen()),
    GetPage(name: smsVerificationScreen, page: () => const SmsVerificationScreen()),
    GetPage(name: profileCompleteScreen, page: () => const ProfileCompleteScreen()),

    //forget password
    GetPage(name: forgotPasswordScreen, page: () => const ForgetPasswordScreen()),
    GetPage(name: verifyPassCodeScreen, page: () => const VerifyForgetPassScreen()),
    GetPage(name: resetPasswordScreen, page: () => const ResetPasswordScreen()),
    GetPage(name: referralScreen, page: () => const ReferralScreen()),
    // GetPage(name: twoFactorScreen, page: () => const TwoFactorVerificationScreen()),
    GetPage(name: faqScreen, page: () => const FaqScreen()),
    GetPage(name: allTicketScreen, page: () => const AllTicketScreen()),
    GetPage(name: ticketDetailsScreen, page: () => const TicketDetailsScreen()),
    GetPage(name: newTicketScreen, page: () => const NewTicketScreen()),
    GetPage(name: previewImageScreen, page: () => PreviewImage(url: Get.arguments)),
  ];

  static Future<void> checkUserStatusAndGoToNextStep(User? user, {bool isRemember = false, String accessToken = "", String tokenType = ""}) async {
    bool needEmailVerification = user?.ev == "1" ? false : true;
    bool needSmsVerification = user?.sv == '1' ? false : true;
    

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

    if (isRemember) {
      await sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, true);
    } else {
      await sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
    }

    await sharedPreferences.setString(SharedPreferenceHelper.userIdKey, user?.id.toString() ?? '-1');
    await sharedPreferences.setString(SharedPreferenceHelper.userEmailKey, user?.email ?? '');
    await sharedPreferences.setString(SharedPreferenceHelper.userPhoneNumberKey, user?.mobile ?? '');
    await sharedPreferences.setString(SharedPreferenceHelper.userNameKey, user?.username ?? '');

    if (accessToken.isNotEmpty) {
      await sharedPreferences.setString(SharedPreferenceHelper.accessTokenKey, accessToken);
      await sharedPreferences.setString(SharedPreferenceHelper.accessTokenType, tokenType);
    }

    bool isProfileCompleteEnable = user?.profileComplete == '0' ? true : false;

    if (isProfileCompleteEnable) {
      Get.offAndToNamed(RouteHelper.profileCompleteScreen);
    } else if (needEmailVerification) {
      Get.offAndToNamed(RouteHelper.emailVerificationScreen);
    } else if (needSmsVerification) {
      Get.offAndToNamed(RouteHelper.smsVerificationScreen);
    } else {
      PushNotificationService object = PushNotificationService(apiClient: Get.find());
      await object.sendUserToken();
      Get.offAndToNamed(RouteHelper.homeScreen, arguments: [true]);
    }
  }
}
