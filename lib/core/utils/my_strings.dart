import 'package:signal_lab/data/model/language/language_model.dart';

class MyStrings{

  static const String hasUpperLetter = "Has uppercase letter";
  static const String hasLowerLetter = "Has lowercase letter";
  static const String hasDigit       = "Has digit";
  static const String hasSpecialChar = "Has special character";
  static const String minSixChar     = "Min of 6 characters";
    static const String downloadDirNotFound             = "Download directory not found";

  static const String hasUpperLette1r            = "one uppercase letter";
  static const String otpFieldEmptyMsg           = "Otp field can't be empty";
  static const String hasLowerLetter1            = "one lowercase letter";
  static const String hasDigit1                  = "one digit";
  static const String cancel                     = "Cancel";
  static const String chooseFile                 = 'Choose File';
  static const String noFileChosen               = 'No file chosen';
    static const String unableToAccessStorage      = 'Unsupported file type';
  static const String unsupportedFileType        = 'Unable to access storage';
  static const String referralCode               = 'Referral Code (optional)';
    static const String supportedFileType               = "Supported File Type:";
  static const String ext                             = ".jpg, .jpeg, .png, .pdf, .doc, .docx";
  static const String emailVerificationSuccess   = 'Email Verification Success';
  static const String accountDeletedSuccessfully = 'Account deleted successfully';
  static const String hasSpecialChar1            = "one special character";
  static const String copiedToClipBoard          = "Copied to your clipboard!";
  static const String setupKey                   = "Setup Key";
  static const String faq                        = "FAQ";
    static const String last                            = "Last";
  static const String addYourAccount             = "Add Your Account";
  static const String verificationSuccess        = 'Verification Success';
  static const String deleteAccount              = 'Delete Account';
    static const String noSupportTicket                 = 'No support ticket found';
  static const String twoFactorMsg               = 'Enter 6-digit code from your two factor authenticator APP.';
  static const String minSixChar1                = "minimum of 6 characters";
   static const String noSupportTicketToShow           = 'Sorry! there are no ticket to show';
  static const String useQRCODETips            = "Use the QR code or setup key on your Google Authenticator app to add your account.";
  static const String useQRCODETips2           = "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.";
  static const String twoFaIconMsg             = "Manage your 2FA security";
  static const String sms                      = 'SMS';
  static const String yourEmail                = 'your email';
  static const String passwordResetEmailSentTo = 'Password reset email sent to';
  static const String strongPass               = "Please enter a strong password";
  static const String emailVerificationFailed  = 'Email Verification Failed';
  static const String enterYourFirstName       = "Enter your first name";
  static const String enterYourLastName        = "Enter your last name";

  static const String agreePolicyMessage = "You must agree with our privacy & policies";


  static const String noSignalFound          = 'No Signal Found';
  static const String noDataFound            = 'No Data Found';
  static const String noReferralFound        = 'No Referral Data Found';
  static const String noTrxlFound            = 'No Transaction Found';
  static const String noDepositFound         = 'No Deposit Found';
  static const String charge                 = 'Charge';
  static const String conversionRate         = 'Conversion Rate';
  static const String depositLimit           = "Deposit Limit";
  static const String enterTransactionNumber = "Enter transaction number";

  static const String approved  = "Approved";
  static const String succeed   = "Succeed";
  static const String pending   = "Pending";
  static const String rejected  = "Rejected";
  static const String initiated = 'Initiated';

      //exit dialog
  static const String exitTitle          = "Do you want to exit\n the app?";
  static const String no                 = "No";
  static const String yes                = "Yes";
    static const String upload                          = "Upload";
  static const String signalName         = "Signal Name";
  static const String searchBySignalName = "Search by signal name";


  static const String newPassEmptyMsg     = "New password can't be empty";
  static const String currentPassEmptyMsg = "Current password can't be empty";


  static const String plsEnterATrxNo       = "Please enter a transaction number";
  static const String searchByTransactions = "Search by transactions";


  static const String latestSignal       = "Latest Signal";
  static const String allSignal          = "All Signals";
  static const String areYourSureToRenew = "Are you sure to renew";
  static const String areYourSureToBuy   = "Are you sure to buy";
  static const String price              = "Price";
  static const String validity           = "Validity";
  static const String days               = "Days";
  static const String yourBalance        = "Your Balance";

  static const String enterAnAmount          = "Please enter an amount";
  static const String selectAnPaymentGateway = "Please select an payment gateway";
  static const String plsSelectOne           = "Please Select One";
  static const String searchReferrals        = "Search Referrals";
  static const String searchByUsername       = "Search by username";

      //registration
  static const String plsSelectACountry = "Select a country";
  static const String mobileEmptyMsg    = "Enter your mobile number";
  static const String enterYourPassword = "Enter your password";
  static const String invalidPassMsg    = "Enter a valid password";
  static const String passNoMatchMsg    = "Password doesn't match";
  static const String enterAValidEmail  = "Enter a valid email";
  static const String enterYourUsername = "Enter your username";
  static const String iAgreeWith        = "I agree with all the";


      // dashboard
  static const String totalTransaction = "Total Transaction";
  static const String totalSignal      = "Total Signal";
  static const String totalDeposit     = "Total Deposit";
  static const String totalReferral    = "Total Referral";
  static const String referralLink     = "Referral Link";
  static const String referrals        = "Referrals";
  static const String details          = "Details";

  static const String logoutSuccessMsg       = 'You have successfully logged out';
  static const String totalBalance           = "Total Balance";
  static const String addDeposit             = 'Add Deposit';
  static const String addPayment             = 'Add Payment';
  static const String paymentNow             = 'Payment Now';
  static const String free                   = 'Free';
  static const String successfullyCodeResend = 'Successfully Code resend';
  static const String resendCodeFail         = 'Fail to resend code';
  static const String somethingWentWrong     = 'Something went wrong';
  static const String searchResult           = 'Search Result';
  static const String resetPassword          = 'Reset Password';
  static const String enterTheCodeSent       = "Enter the code sent to ";
  static const String createYourAccount      = "Let's create your\naccount";
  static const String signInto               = "Please sign in to\ncontinue";
  static const String wellComeTo             = "Welcome to\n$appName";
  static const String weHaveSent             = "We have sent a verification code to\n";
  static const String resetLabelText         = "Your account is verified successfully . Now\nyou can change your password.";          /*"Your account is verified successfully. Now you can change your password.Please enter a strong password and don't share it with anyone";*/
  static const String toRecover              = "To recover your account please provide your email or username to find your account";
  static const String yourEmailAddress       = "your email address";
  static const String yourPhnNumber          = "your phone number";
  static const String submit                 = "Submit";
  static const String enable2Fa              = "Enable 2FA Security";
  static const String disable2Fa             = "Disable 2FA Security";
  static const String forgetPassword         = "Forget Password";
  static const String emailOrUserName        = 'Email or Username';
  static const String profile                = 'Profile';
  static const String about                  = 'About';
  static const String policies               = 'Policies';
  static const String emailVerification      = 'Email Verification';
  static const String emailVerificationMsg   = 'We have sent you an access code via email for email verification';
  static const String toYourEmailAddress     = 'your email address';
  static const String smsVerification        = 'Sms Verification';
  static const String twoFactorAuth          = 'Two Factor Authentication';
  static const String verificationFailed     = 'Verification Failed';
  static const String asAGuest               = 'As a Guest';
  static const String yourRequested          = "You're requested for plan ";
  static const String nowYouHave             = "Now you've to pay ";
  static const String selectAMethod          = "Select a Method";
  static const String paymentMethod          = 'Payment Method ';
    static const String attachment                      = "Attachment";

  static const String getStarted            = 'Get Started';
  
  static const String continue_             = 'Continue';
  static const String pleaseFillOutTheField = 'Please fill out this field';
  static const String login                 = 'Login';
  static const String chooseAFile                     = "Choose a file";
  static const String registration          = 'Registration';
  static const String enterYourName         = 'Enter Your Name';
  static const String enterUserNameOrEmail  = 'Enter Username or Email';
  static const String enterYourUserName     = 'Enter Your Username';
  static const String enterYourPassword_    = 'Enter Your Password';
  static const String enterYourEmail        = 'Enter Your Email';
  static const String enterYourPhone        = "Enter Your Phone Number";
  static const String username              = 'Username';
  static const String password              = 'Password';
  static const String confirmPassword       = 'Confirm Password';
  static const String confirmYourPassword   = 'Confirm your password';
  static const String forgetYourPassword    = 'Forget your password?';
  static const String signIn                = 'Sign In';
  static const String signUp                = 'Sign Up';
  static const String notAccount            = "Don't have an account?";
  static const String rememberMe            = "Remember Me";
  static const String registerNow           = "Register Now";
  static const String address               = "Address";
  static const String state                 = "State";
  static const String zipCode               = "Zip Code";
  static const String city                  = "City";
  static const String updateProfile         = "Update Profile";
  static const String signOut               = "Sign Out";

  static const String firstName         = "First Name";
  static const String lastName          = "Last Name";
  static const String country           = "Country";
  static const String mobile            = "Mobile";
  static const String enterMobileNumber = "Enter mobile number";
  static const String userName          = "Username";
  static const String emailAddress      = "Email Address";
  static const String email             = "Email";
  static const String pickACountry      = "Pick a Country";

  static const String privacyAndPolicy  = "Privacy and Policy";
  static const String termsAndCondition = "Terms and Condition";

  static const String allShows          = "All Shows";
  static const String allMovies         = "All Movies";
  static const String allEpisodes       = "All Episode";
  static const String freeZone          = "Free Zone";
  static const String alreadyInWishlist = "Already in wishlist";
  static const String subscribePLan     = "Subscribe Plan";
  static const String subscribeNow      = "Subscribe Now";

  static const String myHistory = "My History";

      //nav drawer
  static const String profileSettings = "Profile Setting";
  static const String yourDownload    = "Your Download";
  static const String subscription    = "Subscription";
  static const String subscribe       = "Subscribe";
  static const String language        = "Language";
  static const String logout          = "Logout";

  static const String allTV = "All Tv";

      //registration
  static const String createAnAccount = 'Create an account';
  static const String selectCountry   = "Select Country";

      //login
  static const String movieStreamingPlatform = "Movie streaming platform";
  static const String latestSeries           = "Latest Series";

      //nav drawer
  static const String profileSetting  = "Profile Setting";
  static const String profileComplete = "Profile Complete";
  static const String wishList        = "Wishlist";
  static const String history         = "History";
  static const String payment         = "Payment Log";

      //otp verification page
  static const String otpVerification = "OTP Verification";
  static const String otpEmptyMsg     = "OTP field can't be empty";
  static const String notReceiveCode  = "Didn't receive code?";
  static const String requestAgain    = "Request again";

      //menu
  static const String home      = "Home";
  static const String menu      = "Menu";
  static const String twoFactor = "TFA";

      // bottom-nav
  static const String package      = "Package";
  static const String signal       = "Signal";
  static const String vip          = "VIP";
  static const String learning     = "Learning";
  static const String transaction  = "Transaction";


  static const String apply              = "Apply";
  static const String filterBy           = "Filter By";
  static const String clearAll           = "Clear All";
  static const String category           = "Category";
  static const String subCategory        = "Subcategory";
  static const String features           = "Features";
  static const String featuredItem       = "Featured Items";
  static const String sortBy             = "Sort By";
  static const String chooseLanguage     = "Choose Language";
  static const String movieDetails       = "Movie Details";
  static const String playNow            = "Play Now";
  static const String description        = "Description";
  static const String live               = "Live";
  static const String team               = "Team";
  static const String ourTeam            = "Our Team";
  static const String review             = "Review";
  static const String movieDescription   = "Movie Description";
  static const String channelDescription = "Channel Description";
  static const String ipsumMovieDetails  = "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Latest sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum";
  static const String more               = "More";

  static const String token                 = 'Signal_Lab';
  static const String watchingLiveOnPlayLab = 'Watch Live ';

  static const String recommended = "Recommended";
  static const String showMore    = "Show More";

  static const String search           = "Search";
  static const String recentSearch     = "Recent Search";
  static const String recentlyAdded    = "Recently Added";
  static const String yourSearchResult = "Your Search Result";

  static const String yourFilterResult = "Your Filter Result";
  static const String ourFreeZone      = "Our Free Zone";
  static const String latestTrailer    = "Latest Trailer";

  static const String liveTV = "Live TV";

  static const String confirm_ = 'Confirm';
  static const String cancel_  = 'Cancel';
  static const String next     = 'Next';
  static const String skip     = 'Skip';

  static const String success = 'success';
  static const String version = 'Version 1.0';

  static const String appName  = 'Signal Lab';
  static const String themeKey = 'theme';

  static const String emailOtpSuccessMessage = 'Email verified successfully';

      // Form Error
  static RegExp emailValidatorRegExp = 
  RegExp(r"^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
  static const String kEmailNullError       = "Please enter your email";
  static const String kInvalidEmailError    = "Please enter valid Email";
  static const String kPassNullError        = "Please enter your password";
  static const String kInvalidPassError     = "Please enter a valid password";
  static const String kOnlyChangePassError  = "Password must be 6 character";
  static const String kMatchPassError       = "Password doesn't match";
  static const String kFirstNameNullError   = "Please enter first name";
  static const String kLastNameNullError    = "Please enter last name";
  static const String kShortUserNameError   = "Username must be 6 character";
  static const String kUserNameNullError    = "Please enter username";
  static const String kPhoneNumberNullError = "Please enter your phone number";
  static const String currentPassNullError  = "Current pass null ";
  static const String phoneNumber           = "Phone Number";
  static const String verify                = 'Verify';
  static const String passVerification      = 'Password Verification';
  static const String didNotReceiveCode     = "Didn't receive the code?";
  static const String resend                = "RESEND";
  static const String plsFillProperly       = "Please fill up all the cells properly";
  static const String plsEnter              = "Please Enter ";
  static const String emailOrUsername       = 'Email or user name';
  static const String loginFailedTryAgain   = 'user login failed , pls try again';
  static const String error                 = 'error';
  static const String type                  = 'Type';
  static const String trxType               = 'Trx Type';
  static const String remark                = 'Remark';

      // pricing-plan screen
  static const String basic      = "Basic";
  static const String popular    = "POPULAR";
  static const String choosePlan = "Choose Plan";

      // appbar title
  static const String packagesPlan       = "Package Pricing Plan";
  static const String userMenu           = "User Menu";
  static const String userProfile        = "User Profile";
  static const String editProfile        = "Edit Profile";
  static const String changePassword     = "Change Password";
  static const String transactionHistory = "Transaction History";
  static const String depositHistory     = "Deposit History";
  static const String depositNow         = "Deposit Now";
  static const String privacyPolicy      = "Privacy Policy";

      // privacy-policy screen title
  static const String ourPrivacyPolicy        = "Our Privacy Policy";
  static const String howLongWeRetain         = "How Long We Retain";
  static const String yourData                = "What we don't do with your data";
  static const String protectionActCompliance = "Protection Act Compliance";
  static const String refundPolicy            = "Payment/Refund Policy";
  static const String paymentPolicy           = "Payment Policy";

      // transaction history screen
  static const String all                   = "All";
  static const String transactionNumber     = "Transaction Number";
  static const String transactionHinText    = "Enter Transaction Number";
  static const String transactionButtonName = "Filters";
  static const String trx                   = "TRX";
  static const String date                  = "Date";
  static const String amount                = "Amount";
  static const String postBalance           = "Post Balance";

      // deposit history screen
  static const String gateway         = "Gateway";
  static const String conversion      = "Conversion";
  static const String depositHintText = "Search by transaction";

      // alert-dialog
  static const String transactionAlertTitle = "Transaction Details";
  static const String depositAlertTitle     = "Deposit Details";
  static const String alertDialogContent    = "Bonorum Malor The terms of Good  popular erowri sheets containing discovered";

      // login-screen
  static const String loginTitle     = "Let’s sign you in.";
  static const String loginSologan   = "Welcome back, please enter\nyour details";
  static const String forgotPassword = "Forgot password?";
  static const String dontAccount    = "Don't have an account?";

      // signup-screen
  static const String signUpTitle    = "Start your journey";
  static const String signUpSologan  = "Create your account,please enter\n your details";
  static const String alreadyAccount = "Already Have An Account?";
  static const String signInNow      = "Sign In now";


  static const String resetYourPassword = "Reset Your Password";
  static const String passwordEmptyMsg  = "Password filed can't be empty";
  static const String verifyEmailMsg    = "A 6 digit verification code sent to your email address";
  static const String noCodeReceive     = "Didn't receive the code?";

  static const String emailOrUsernameEmptyMsg = "Email or username field can't be empty";

  static const String passwordVerification = "Password Verification" ;
  static const String passResetMailSendTo  = "Password reset email send to" ;
  static const String toYourEmail          = "your email" ;
  static const String recoverAccount       = "Recover Account" ;
  static const String resetPassMsg         = "Enter your email or username below to receive a password reset verification code" ;

      // onboard-screen
  static const String onboardTitle    = "Discover Your Dream Here";
  static const String onboardSubtitle = "Hidden in the middle of text. All the Lorem Ipsum generators on the Internet";

      // create password
  static const String createNewPass            = "Create new password";
  static const String createNewPassDescription = "Your new password must be different\nfrom previous used password";
  static const String currentPassword          = "Current Password";
  static const String newPassword              = "New Password";

  static const String selectGateway    = "Select Gateway";
  static const String selectOne        = "Select One";
  static const String limit            = "Limit";
  static const String payable          = "Payable";
  static const String telegramUsername = "Telegram Username";
  static const String signals          = "Signals";

  static const String close                    = "Close";
  static const String confirm                  = "Confirm";
  static const String requestFail              = "Request Fail";
  static const String requestSuccess           = "Request Success";
  static const String planRenewSuccessMsg      = "You have renewed plan successfully";
  static const String referralLinkCopied       = "Copied Referral Link";
  static const String download                 = "Download";
  static const String ticketDetails            = 'Ticket Details';
  static const String ticket                   = 'Ticket';
  static const String yourReply                = 'Your Reply';
  static const String reply                    = 'Reply';
  static const String attachments              = 'Attachments';
  static const String postedOn                 = 'Posted on';
  static const String answered                 = 'Answered';
  static const String open                     = 'Open';
  static const String replied                  = 'Replied';
  static const String closed                   = 'Closed';
  static const String addNewTicket             = 'Create Ticket';
  static const String subject                  = 'Subject';
  static const String enterYourSubject         = 'Enter your subject';
  static const String priority                 = 'Priority';
  static const String message                  = 'Message';
  static const String typeHere                 = 'Type here';
  static const String help                     = 'Help';
  static const String enterYourMessage         = 'Enter your message';
  static const String subjectRequired          = 'Subject is required';
  static const String messageRequired          = 'Message is required';
  static const String high                     = 'High';
  static const String medium                   = 'Medium';
  static const String low                      = 'Low';
  static const String contactUs                = 'Contact Us';
  static const String supportTicket            = 'Support Ticket';
  static const String ourCommunityGroup        = 'Our community group';
  static const String complaintBox             = 'Complaint Box';
  static const String status                   = 'Status';
  static const String lastReply                = 'Last Reply';
  static const String replyTicket              = "Ticket Details";
  static const String customerReply            = "Customer Reply";
  static const String repliedSuccessfully      = "Replied successfully";
  static const String fileNotFound             = 'file Not Found';
  static const String notifications            = 'Notifications';
  static const String noTicketFound            = "No Ticket Found";
  static const String noMSgFound               = "No Message Found";
  static const String you                      = "You";
  static const String admin                    = "Admin";
  static const String noInternet                      = 'No internet connection';
  static const String replyTicketEmptyMsg      = "Reply ticket can't be empty";
  static const String ticketCreateSuccessfully = 'Ticket created successfully';
  static const String cancelTicketMessage      = 'Are you sure you want to close the ticket';
  static const String or                       = 'OR';
  static const String signInWithGoogle         = "Sign In with Google";
  static const String signInWithFacebook       = "Sign In with Facebook";
  static const String signInWithLinkedin       = "Sign In with Linkedin";
    static const String noDocOpenerApp                  = "No doc openner apps";
  static const String deleteYourAccount             = 'Delete your Account';
  static const String deleteBottomSheetSubtitle     = "You will lose all of your data by deleting your account. This action cannot be undone.";
  static const String areYouSureWantToDeleteAccount = "Are you sure you want to ";
  static const String afterDeleteYouCanBack         = "After Yes Your can't  undo yourself";
  static const String successfully                  = "Successful";
    static const String unAuthorized                    = 'Unauthorized';
      static const String badResponseMsg                  = 'Bad Response Format!';
  static const String serverError                     = 'Server Error';

   static List<MyLanguageModel> myLanguages      = [
    MyLanguageModel(languageName: 'English', countryCode: 'US', languageCode: 'en'),
    MyLanguageModel(languageName: 'Arabic', countryCode: 'SA', languageCode: 'ar'),
  ];

  // -- On Boarding Text
  static const String tOnBoardingTitle1 = "Build Awesome Apps";
  static const String tOnBoardingTitle2 = "Learn from YouTube";
  static const String tOnBoardingTitle3 = "Get Code & Resources";
  static const String tOnBoardingSubTitle1 = "Let's start your journey with us on this amazing and easy platform.";
  static const String tOnBoardingSubTitle2 = "Get Video Tutorials of each topic to learn things easily.";
  static const String tOnBoardingSubTitle3 = "Save time by just copy pasting complete apps you learned from videos.";
  static const String tOnBoardingCounter1 = "1/3";
  static const String tOnBoardingCounter2 = "2/3";
  static const String tOnBoardingCounter3 = "3/3";

}