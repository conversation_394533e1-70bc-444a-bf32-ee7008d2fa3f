import 'package:flutter/widgets.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:signal_lab/core/utils/dimensions.dart';
import 'package:signal_lab/core/utils/my_color.dart';

TextStyle interNormalExtraSmall = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontExtraSmall // 10
);

TextStyle interNormalSmall = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontSmall // 12
);

 TextStyle interNormalDefault = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontDefault // 14
);

TextStyle interNormalDefaultLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontDefaultLarge // 16
);

TextStyle interNormalMediumLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontMediumLarge // 18
);

TextStyle interNormalLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontLarge // 20
);

TextStyle interNormalExtraLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontExtraLarge // 22
);

TextStyle interNormalOverLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontOverLarge // 24
);

TextStyle interNormalHeader = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontHeader // 26, 28, 30, 32
);

TextStyle interNormalHeaderLarge = GoogleFonts.inter(

    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w400,
    fontSize: Dimensions.fontHeaderLarge // 40 - up
);

TextStyle interSemiBoldSmall = GoogleFonts.inter(
    color: MyColor.colorWhite,
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontSmall
);

TextStyle interSemiBoldLarge = GoogleFonts.inter(
    color:  MyColor.colorWhite,
    fontWeight: FontWeight.w600,
    fontSize: Dimensions.fontLarge
);