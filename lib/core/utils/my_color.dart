import 'package:flutter/material.dart';

class MyColor{

  static const Color primaryColor = Color(0xffFB3640);

  static const Color backgroundColor =  Color(0xff0D222B);
  static const Color appBarBgColor =  Color(0xff192D36);
  static const Color alertDialogTitleBg = Color(0xff14232A);
  static const Color otpBgColor = Color(0xFF172B33);

  static const Color cardBgColor =  Color(0xff192D36);
  static const Color circleBorder = Color(0xff30424a);
    static const Color cancelRedColor = Color(0xffFF3B30);

  // text color
  static const Color labelTextColor =  Color(0xffCFD3D5);
  static const Color hintTextColor =  Color(0xff44555B);

    static const Color inputFillColor = Colors.transparent;

  static const Color dividerColor =  Color(0xff30424A);
  static const Color colorBlack = Color(0xFF000000);
    static const Color ticketDateColor = Color(0xff888888);
  static const Color colorWhite = Color(0xFFFFFFFF);
  static const Color whiteTextColor = colorGrey;
  static const Color colorHint = Color(0xFF52575C);
  static const Color colorGrey = Color(0xFFA0A4A8);
    static const Color delteBtnColor = primaryColor;
     static const Color ticketDetails = Color(0xff5D5D5D);
    static const Color textFieldDisableBorderColor = Color(0xffCFCEDB);

  static  Color transparentColor = Colors.transparent;
  static  Color gbr = Colors.black.withOpacity(0.3);

  static const Color pendingColor = Color(0xFFfcb44f);
  static const Color borderColor = Color(0xFFEFEFEF);
  static const Color highPriorityPurpleColor = Color(0xFF7367F0);
  static const Color purpleAcccent = Color(0xFF7C4DFF);
  static const Color bgColorLight = Color(0xFFf2f2f2);
  static const Color closeRedColor = Color(0xFFEA5455);
  static const Color cardColor = Color(0xFFFFFFFF);
    static const Color delteBtnTextColor = Color(0xff6C3137);
    static const Color textColor = Color(0xFF000000);
      static const Color bodyTextColor = Color(0xFF747475);
  static const Color greenSuccessColor = greenP;
  static const Color redCancelTextColor = Color(0xFFF93E2C);
  static const Color red = Color(0xFFD92027);
    static const Color contentTextColor = Color(0xff777777);
  static const Color greenP = Color(0xFF28C76F);
    static const Color shadowColor = Color(0xffEAEAEA);
    static const Color textFieldEnableBorderColor = primaryColor;

      static Color greyColorWithShadeFiveHundred = Colors.grey.shade500;
  static Color greyColorWithShadeFourHundred = Colors.grey.shade400;


  // -- ON-BOARDING COLORS
  static const Color onBoardingPage1Color = Colors.white;
  static const Color onBoardingPage2Color = Color(0xfffddcdf);
  static const Color onBoardingPage3Color = Color(0xffffdcbd);

  static Color getPrimaryColor() {
    return primaryColor;
  }

   static Color getTextFieldDisableBorder() {
    return textFieldDisableBorderColor;
  }
    static Color getShadowColor() {
    return  shadowColor;
  }
  
  static Color getContentColor() {
    return contentTextColor;
  }
  static Color getTransparentColor() {
    return transparentColor;
  }
    static Color getContentTextColor() {
    return contentTextColor;
  }
    static Color getHeadingTextColor() {
    return MyColor.colorWhite;
  }
   static Color getGreyColor() {
    return MyColor.colorGrey;
  }
   static Color getWhiteColor() {
    return MyColor.colorWhite;
  }
    static Color getGreyColorwithShade500() {
    return greyColorWithShadeFiveHundred;
  }

    static Color getCardBgColor() {
    return cardColor;
  }
  static Color getScreenBgColor() {
    return backgroundColor;
  }
    static Color getDeleteButtonTextColor() {
    return delteBtnTextColor;
  }
  static Color getDeleteButtonColor() {
    return delteBtnColor;
  }
    static Color getRedCancelTextColor() {
    return redCancelTextColor;
  }
    static Color getRedColor() {
    return red;
  }
    static Color getLabelTextColor() {
    return labelTextColor;
  }
    static Color getTextColor() {
    return  textColor;
  }
 static Color getTicketDetailsColor() {
    return ticketDetails;
  }
   static Color getBorderColor() {
    return borderColor;
  }

  static Color getPrupleColor() {
    return highPriorityPurpleColor;
  }
   static Color getGreenColor() {
    return greenP;
  }
   static Color getPendingColor() {
    return MyColor.pendingColor;
  }
  static Color getTextFieldEnableBorder() {
    return textFieldEnableBorderColor;
  }
    static Color getPrupleAccentColor() {
    return purpleAcccent;
  }
  static Color getHintTextColor() {
    return hintTextColor;
  }
  static Color getBodyTextColor() {
    return bodyTextColor;
  }



}