import 'package:flutter/cupertino.dart';

class Dimensions {

  // font-size
  static const double fontExtraSmall   = 10.00;
  static const double fontSmall        = 12.00;
  static const double fontDefault      = 14.00;
  static const double font15           = 15.00;
  static const double fontDefaultLarge = 16.00;
  static const double fontMediumLarge  = 18.00;
  static const double fontLarge        = 20.00;
  static const double fontExtraLarge   = 22.00;
  static const double fontOverLarge    = 24.00;
  static const double fontHeader       = 26.00;
  static const double fontHeader1      = 28.00;
  static const double fontHeader2      = 30.00;
  static const double fontHeader3      = 32.00;
  static const double fontHeaderLarge  = 45.00;
    static const double defaultButtonH = 55;
  static const double defaultRadius    = 6;
    static const double textToTextSpace = 8;

  // widget space
  static const double space3 = 3;
  static const double space5 = 5;
  static const double space10 = 10;
  static const double space15 = 15;
  static const double space20 = 20;
  static const double space25 = 25;
  static const double space30 = 30;
  static const double space35 = 35;
  static const double space40 = 40;
  static const double space45 = 45;
  static const double space50 = 50;

  static const double horizontalPaddingForLV  = 12;
  static const double authTextSize            = 20;
  static const double verticalPaddingForLV    = 10;
  static const double radius                  = 10;

  static const double horizontalMargin        = 6;
  static const double verticalMargin          = 6;
  static const double fieldHeight             = 50;

  static const double mediumRadius = 8;
  static const double largeRadius = 12;
  static const double extraRadius = 16;

  static const EdgeInsets padding             = EdgeInsets.symmetric(horizontal: horizontalPaddingForLV,vertical: verticalPaddingForLV);
  static const EdgeInsets lvContainerMargin   = EdgeInsets.symmetric(horizontal: horizontalMargin,vertical: verticalMargin);
  static const EdgeInsets defaultPaddingHV = EdgeInsets.symmetric(vertical: space20, horizontal: space15);

  static const EdgeInsets dialogContainerMargin = EdgeInsets.symmetric(horizontal: horizontalPaddingForLV,vertical: verticalPaddingForLV);

  static const double cornerRadius      = 4;

  static const double marginTop         = 12;
  static const SizedBox labelSizedBox   = SizedBox(height: 15,);
  static const double pageTopMargin     = 15;
  static const double pageLeftRightMargin=10;

  static const double screenPadding = 15;


  static const double homePageLeftMargin=10;
  static const double homePageRightMargin=10;

  static const double spaceBetweenCategory=15;
  static const double spaceBetweenTextAndImage=10;
  static const double textFieldRadius=4;
  static const double badgeRadius = 4;

}
