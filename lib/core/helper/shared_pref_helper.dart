class SharedPreferenceHelper{

  static const String accessTokenKey='access_token';
  static const String userIdKey='user_id';
  static const String accessTokenType='access_type';
  static const String resetPassTokenKey='reset_pass_token';
  static const String userEmailKey='user_email';
  static const String userNameKey='user_name';
  static const String userPhoneNumberKey='user_phone_number';



  static const String rememberMeKey='remember me';
  static const String generalSettingKey='general-setting-key';

  static const String token             = 'token';
  static const String fcmDeviceKey      = 'device-key';
  static const String languageImagePath = 'language_image_path';
  static const String languageCode      = 'language_code';
  static const String languageKey       = 'language-key';
  static const String languageListKey   = 'language-list-key';
  static const String countryCode       = 'country_code';
}